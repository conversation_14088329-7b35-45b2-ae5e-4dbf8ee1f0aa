# Core Django Framework
Django>=4.2,<5.0

# Database
psycopg2-binary>=2.9.5

# Django REST Framework for API development
djangorestframework>=3.14.0

# CORS handling for frontend/mobile apps
django-cors-headers>=4.0.0

# JWT Authentication for mobile APIs
djangorestframework-simplejwt>=5.2.0

# Environment variable management
python-decouple>=3.8

# Image processing for shopping/streaming features
Pillow>=10.0.0

# Additional utilities
django-extensions>=3.2.0

# Development dependencies
django-debug-toolbar>=4.0.0

# Security enhancements
django-ratelimit>=4.0.0

# API documentation
drf-spectacular>=0.26.0

# Timezone support
pytz>=2023.3

# For handling file uploads and media
django-storages>=1.13.0

# For better admin interface
django-admin-interface>=0.19.0

# For handling environment-specific settings
django-environ>=0.10.0
