@echo off
REM Kominote Development Setup Script
REM This script sets up the development environment

echo ========================================
echo Kominote Development Setup
echo ========================================

echo Activating virtual environment...
call kominote_env\Scripts\activate

echo Installing/updating dependencies...
pip install -r requirements.txt

echo Running migrations...
python manage.py migrate

echo Collecting static files...
python manage.py collectstatic --noinput

echo ========================================
echo Setup complete! 
echo ========================================
echo.
echo To start the development server, run:
echo python manage.py runserver
echo.
echo Admin panel: http://127.0.0.1:8000/admin/
echo API docs: http://127.0.0.1:8000/api/docs/
echo ========================================

pause
