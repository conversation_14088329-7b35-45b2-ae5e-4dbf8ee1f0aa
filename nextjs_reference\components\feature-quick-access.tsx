import Link from "next/link"
import { Play, ShoppingBag, Users, Calendar, Briefcase } from "lucide-react"

type FeatureType = "streaming" | "shopping" | "community" | "events" | "jobs"

interface FeatureQuickAccessProps {
  currentFeature: FeatureType
  className?: string
}

export default function FeatureQuickAccess({ currentFeature, className }: FeatureQuickAccessProps) {
  const features = [
    {
      type: "streaming" as FeatureType,
      name: "Streaming",
      href: "/streaming",
      icon: <Play className="h-6 w-6" />,
      bgColor: "bg-blue-100 group-hover:bg-blue-200",
      textColor: "text-blue-600",
    },
    {
      type: "shopping" as FeatureType,
      name: "Shopping",
      href: "/shopping",
      icon: <ShoppingBag className="h-6 w-6" />,
      bgColor: "bg-emerald-100 group-hover:bg-emerald-200",
      textColor: "text-emerald-600",
    },
    {
      type: "community" as FeatureType,
      name: "Community",
      href: "/community",
      icon: <Users className="h-6 w-6" />,
      bgColor: "bg-purple-100 group-hover:bg-purple-200",
      textColor: "text-purple-600",
    },
    {
      type: "events" as FeatureType,
      name: "Events",
      href: "/events",
      icon: <Calendar className="h-6 w-6" />,
      bgColor: "bg-amber-100 group-hover:bg-amber-200",
      textColor: "text-amber-600",
    },
    {
      type: "jobs" as FeatureType,
      name: "Jobs",
      href: "/jobs",
      icon: <Briefcase className="h-6 w-6" />,
      bgColor: "bg-red-100 group-hover:bg-red-200",
      textColor: "text-red-600",
    },
  ]

  // Filter out current feature
  const filteredFeatures = features.filter((feature) => feature.type !== currentFeature)

  return (
    <div className={`grid grid-cols-2 sm:grid-cols-4 gap-4 ${className}`}>
      {filteredFeatures.map((feature) => (
        <Link
          key={feature.type}
          href={feature.href}
          className="group flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div
            className={`h-12 w-12 rounded-full flex items-center justify-center mb-2 group-hover:bg-opacity-80 ${feature.bgColor}`}
          >
            <span className={feature.textColor}>{feature.icon}</span>
          </div>
          <span className="text-sm font-medium">{feature.name}</span>
        </Link>
      ))}
    </div>
  )
}

