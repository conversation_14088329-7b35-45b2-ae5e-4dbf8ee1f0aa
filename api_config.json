{"api": {"version": "1.0.0", "base_url": "http://127.0.0.1:8000/api/", "documentation": {"swagger": "http://127.0.0.1:8000/api/docs/", "redoc": "http://127.0.0.1:8000/api/redoc/", "schema": "http://127.0.0.1:8000/api/schema/"}, "authentication": {"type": "JWT", "token_endpoint": "http://127.0.0.1:8000/api/auth/token/", "refresh_endpoint": "http://127.0.0.1:8000/api/auth/token/refresh/", "verify_endpoint": "http://127.0.0.1:8000/api/auth/token/verify/"}, "endpoints": {"accounts": {"base": "/api/accounts/", "register": "/api/accounts/register/", "profile": "/api/accounts/profile/", "settings": "/api/accounts/settings/"}, "streaming": {"base": "/api/streaming/", "movies": "/api/streaming/movies/", "series": "/api/streaming/series/", "episodes": "/api/streaming/episodes/", "watchlist": "/api/streaming/watchlist/"}, "shopping": {"base": "/api/shopping/", "products": "/api/shopping/products/", "categories": "/api/shopping/categories/", "vendors": "/api/shopping/vendors/", "cart": "/api/shopping/cart/", "orders": "/api/shopping/orders/"}, "community": {"base": "/api/community/", "posts": "/api/community/posts/", "comments": "/api/community/comments/", "groups": "/api/community/groups/", "messages": "/api/community/messages/"}, "events": {"base": "/api/events/", "list": "/api/events/", "categories": "/api/events/categories/", "bookings": "/api/events/bookings/"}, "jobs": {"base": "/api/jobs/", "listings": "/api/jobs/listings/", "applications": "/api/jobs/applications/", "categories": "/api/jobs/categories/"}}, "pagination": {"page_size": 20, "max_page_size": 100}, "rate_limiting": {"requests_per_minute": 60, "requests_per_hour": 1000}}, "mobile_app": {"supported_platforms": ["iOS", "Android"], "frameworks": ["<PERSON><PERSON>", "Flutter", "React Native"], "minimum_versions": {"ios": "12.0", "android": "21"}}, "features": {"streaming": {"name": "Streaming", "description": "Local and international video content", "icon": "play-circle", "color": "#3b82f6"}, "shopping": {"name": "Shopping", "description": "Local vendors and artisans marketplace", "icon": "shopping-bag", "color": "#10b981"}, "community": {"name": "Community", "description": "Social features and discussions", "icon": "users", "color": "#8b5cf6"}, "events": {"name": "Events", "description": "Local events and activities", "icon": "calendar", "color": "#f59e0b"}, "jobs": {"name": "Jobs", "description": "Employment opportunities in Seychelles", "icon": "briefcase", "color": "#ef4444"}}, "seychelles": {"timezone": "Indian/Mahe", "currency": "SCR", "languages": ["English", "French", "Creole"], "country_code": "SC"}}