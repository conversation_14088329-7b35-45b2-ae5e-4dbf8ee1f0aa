import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { CheckCircle, Truck, Calendar, MapPin, CreditCard, FileText, ArrowRight, Printer } from "lucide-react"
import FeatureNavigation from "@/components/feature-navigation"
import { Messenger } from "@/components/messenger"

export default function OrderConfirmationPage() {
  // In a real app, you would fetch order data from a state management solution or API
  const order = {
    id: "ORD-12345-67890",
    date: "March 29, 2025",
    status: "Processing",
    paymentMethod: "Credit Card (•••• 4242)",
    shippingMethod: "Standard Delivery (3-5 business days)",
    estimatedDelivery: "April 3-5, 2025",
    items: [
      {
        id: "coconut-bowl-01",
        name: "Handmade Coconut Bowl",
        price: 24.99,
        quantity: 2,
        image: "/placeholder.svg?height=100&width=100",
        vendor: "Island Crafts Co.",
      },
      {
        id: "bamboo-straw-01",
        name: "Reusable Bamboo Straws (Set of 8)",
        price: 12.99,
        quantity: 1,
        image: "/placeholder.svg?height=100&width=100",
        vendor: "Eco Seychelles",
      },
      {
        id: "palm-plate-01",
        name: "Palm Leaf Plates (Set of 4)",
        price: 29.99,
        quantity: 1,
        image: "/placeholder.svg?height=100&width=100",
        vendor: "Island Crafts Co.",
      },
    ],
    subtotal: 92.96,
    shipping: 0,
    tax: 13.94,
    total: 106.9,
    shippingAddress: {
      name: "Marie Dubois",
      address: "123 Ocean View Road",
      city: "Victoria",
      postalCode: "SC-01234",
      phone: "+248 4 123 456",
      email: "<EMAIL>",
    },
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Simplified for this page */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
          </div>
        </div>
      </header>

      <main className="container px-4 py-8 md:px-6">
        <div className="max-w-3xl mx-auto">
          {/* Success Message */}
          <div className="bg-white rounded-lg shadow-sm p-8 text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-emerald-600" />
            </div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">Order Confirmed!</h1>
            <p className="text-gray-600 mb-6">
              Thank you for your purchase. Your order has been received and is being processed.
            </p>
            <div className="inline-block bg-gray-100 rounded-lg px-4 py-2 text-lg font-medium">Order #{order.id}</div>
            <p className="text-sm text-gray-500 mt-4">
              A confirmation email has been sent to {order.shippingAddress.email}
            </p>
          </div>

          {/* Order Details */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-medium">Order Details</h2>
                <Button variant="outline" size="sm" className="gap-2">
                  <Printer className="h-4 w-4" />
                  Print Receipt
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <Calendar className="h-5 w-5 text-emerald-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Order Date</p>
                      <p className="text-sm text-gray-600">{order.date}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <FileText className="h-5 w-5 text-emerald-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Order Status</p>
                      <p className="text-sm text-gray-600">{order.status}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <CreditCard className="h-5 w-5 text-emerald-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Payment Method</p>
                      <p className="text-sm text-gray-600">{order.paymentMethod}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <Truck className="h-5 w-5 text-emerald-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Shipping Method</p>
                      <p className="text-sm text-gray-600">{order.shippingMethod}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <Calendar className="h-5 w-5 text-emerald-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Estimated Delivery</p>
                      <p className="text-sm text-gray-600">{order.estimatedDelivery}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <MapPin className="h-5 w-5 text-emerald-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Shipping Address</p>
                      <p className="text-sm text-gray-600">
                        {order.shippingAddress.name}
                        <br />
                        {order.shippingAddress.address}
                        <br />
                        {order.shippingAddress.city}, {order.shippingAddress.postalCode}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="my-6" />

              <h3 className="font-medium mb-4">Order Items</h3>
              <div className="space-y-4 mb-6">
                {order.items.map((item) => (
                  <div key={item.id} className="flex gap-4">
                    <div className="relative h-20 w-20 rounded-md overflow-hidden bg-gray-100 flex-shrink-0">
                      <Image src={item.image || "/placeholder.svg"} alt={item.name} fill className="object-cover" />
                    </div>
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:justify-between gap-2">
                        <div>
                          <h4 className="font-medium text-gray-900">
                            <Link href={`/shopping/product/${item.id}`} className="hover:text-emerald-600">
                              {item.name}
                            </Link>
                          </h4>
                          <p className="text-sm text-gray-500">Vendor: {item.vendor}</p>
                          <p className="text-sm text-gray-500">Quantity: {item.quantity}</p>
                        </div>
                        <p className="font-medium text-emerald-600">${(item.price * item.quantity).toFixed(2)}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <Separator className="my-6" />

              <div className="space-y-3 max-w-xs ml-auto">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">${order.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium">{order.shipping === 0 ? "Free" : `$${order.shipping.toFixed(2)}`}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax (15%)</span>
                  <span className="font-medium">${order.tax.toFixed(2)}</span>
                </div>
                <Separator className="my-3" />
                <div className="flex justify-between">
                  <span className="font-medium">Total</span>
                  <span className="font-bold text-emerald-600">${order.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
            <div className="p-6">
              <h2 className="text-lg font-medium mb-4">What's Next?</h2>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="h-6 w-6 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-emerald-600">1</span>
                  </div>
                  <div>
                    <p className="font-medium">Order Processing</p>
                    <p className="text-sm text-gray-600">
                      Your order is being processed and prepared for shipping. You'll receive an email when your order
                      ships.
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="h-6 w-6 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-emerald-600">2</span>
                  </div>
                  <div>
                    <p className="font-medium">Shipping</p>
                    <p className="text-sm text-gray-600">
                      Your order will be shipped via {order.shippingMethod.split("(")[0].trim()}. You can track your
                      order from your account dashboard.
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="h-6 w-6 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-emerald-600">3</span>
                  </div>
                  <div>
                    <p className="font-medium">Delivery</p>
                    <p className="text-sm text-gray-600">
                      Estimated delivery: {order.estimatedDelivery}. Someone may need to be present to receive the
                      package.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Link href="/shopping/account/orders" className="flex-1">
              <Button variant="outline" className="w-full">
                View Order History
              </Button>
            </Link>
            <Link href="/shopping" className="flex-1">
              <Button className="w-full bg-emerald-600 hover:bg-emerald-700">
                Continue Shopping
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </main>

      {/* Footer - Simplified for this page */}
      <footer className="bg-white border-t py-8 mt-12">
        <div className="container px-4 md:px-6">
          <div className="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Shopping. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Terms of Service
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Privacy Policy
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="shopping" colorScheme="emerald" />

      {/* Messenger */}
      <Messenger variant="light" />
    </div>
  )
}

