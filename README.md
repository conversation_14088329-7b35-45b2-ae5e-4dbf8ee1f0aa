# Kominote - Seychelles' All-in-One Digital Hub

## 🌴 About Kominote

Kominote is Seychelles' comprehensive digital platform that brings together entertainment, commerce, and community in one place. The platform features:

- **🎬 Streaming** - Local and international content
- **🛒 Shopping** - Local vendors and artisans marketplace  
- **👥 Community** - Social features and discussions
- **📅 Events** - Local events and activities
- **💼 Jobs** - Employment opportunities in Seychelles

## 🚀 Phase 1 Complete: Django Project Setup & Infrastructure

### ✅ Completed Tasks

1. **Django Project Initialization**
   - Created Django 4.2.21 project with modern configuration
   - Set up virtual environment with Python 3.13.3
   - Configured project structure for scalability

2. **Core Dependencies Installed**
   - Django REST Framework for API development
   - PostgreSQL support (psycopg2-binary)
   - JWT authentication (djangorestframework-simplejwt)
   - CORS handling (django-cors-headers)
   - API documentation (drf-spectacular)
   - Enhanced admin interface (django-admin-interface)
   - Development tools (debug toolbar, extensions)

3. **Configuration Setup**
   - Environment variable management with python-decouple
   - Separate development/production settings
   - SQLite for development, PostgreSQL ready for production
   - Seychelles timezone (Indian/Mahe)
   - Comprehensive security settings

4. **API Infrastructure**
   - REST Framework configured with JWT authentication
   - API documentation with Swagger/ReDoc
   - CORS configured for mobile app development
   - Pagination and proper response formatting

5. **Project Structure**
   - Templates directory for Django templates
   - Static files configuration
   - Media files handling for user uploads
   - Ready for Django apps creation

### 🔧 Technical Stack

- **Backend**: Django 4.2.21 (LTS)
- **Database**: SQLite (dev) / PostgreSQL (production)
- **API**: Django REST Framework 3.16.0
- **Authentication**: JWT tokens for mobile apps
- **Documentation**: drf-spectacular (Swagger/ReDoc)
- **Admin**: Enhanced Django admin interface

### 🌐 Available Endpoints

- **Admin Interface**: http://127.0.0.1:8000/admin/
- **API Root**: http://127.0.0.1:8000/api/
- **API Documentation**: http://127.0.0.1:8000/api/docs/
- **API Schema**: http://127.0.0.1:8000/api/redoc/
- **JWT Token**: http://127.0.0.1:8000/api/auth/token/

### 👤 Admin Credentials

- **Username**: admin
- **Email**: <EMAIL>
- **Password**: admin123

## 🚀 Quick Start

1. **Activate Virtual Environment**
   ```bash
   kominote_env\Scripts\activate
   ```

2. **Run Development Server**
   ```bash
   python manage.py runserver
   ```

3. **Access the Application**
   - Main site: http://127.0.0.1:8000/
   - Admin panel: http://127.0.0.1:8000/admin/
   - API docs: http://127.0.0.1:8000/api/docs/

## 📋 Next Steps (Phase 2)

1. Create Django apps for each feature:
   - `accounts` - User management
   - `landing` - Landing page content
   - `streaming` - Video content
   - `shopping` - E-commerce
   - `community` - Social features
   - `events` - Event management
   - `jobs` - Job listings
   - `api` - Centralized API endpoints

2. Design and implement database models
3. Create API endpoints for mobile apps
4. Develop Django templates for web interface

## 🔒 Environment Variables

Key environment variables in `.env`:
- `SECRET_KEY` - Django secret key
- `DEBUG` - Debug mode (True/False)
- `USE_SQLITE` - Database choice (True for SQLite, False for PostgreSQL)
- `CORS_ALLOWED_ORIGINS` - Allowed origins for CORS
- `TIME_ZONE` - Timezone setting (Indian/Mahe)

## 📱 Mobile App Ready

The API infrastructure is configured for iOS and Android mobile app development using:
- **Ionic Framework**
- **Flutter**
- JWT authentication
- RESTful API endpoints
- Proper CORS configuration

---

**Status**: Phase 1 Complete ✅  
**Next**: Phase 2 - Django Apps Creation  
**Target**: Full-featured Seychelles digital platform
