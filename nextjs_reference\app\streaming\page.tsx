"use client"

import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Bell, Play, Info, ChevronRight, Filter } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { UserNav } from "@/components/user-nav"
import FeatureNavigation from "@/components/feature-navigation"
import { Messenger } from "@/components/messenger"

export default function StreamingPage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-gradient-to-b from-black/80 to-transparent backdrop-blur-sm">
        <div className="container mx-auto px-4 md:px-6 flex h-16 items-center justify-between">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <nav className="hidden md:flex gap-6">
              <Link href="/streaming" className="text-sm font-medium text-white hover:text-blue-400">
                Home
              </Link>
              <Link href="/streaming/series" className="text-sm font-medium text-gray-300 hover:text-white">
                Series
              </Link>
              <Link href="/streaming/movies" className="text-sm font-medium text-gray-300 hover:text-white">
                Movies
              </Link>
              <Link href="/streaming/new" className="text-sm font-medium text-gray-300 hover:text-white">
                New & Popular
              </Link>
              <Link href="/streaming/mylist" className="text-sm font-medium text-gray-300 hover:text-white">
                My List
              </Link>
            </nav>
          </div>
          <div className="flex items-center gap-4">
            <div className="relative hidden md:flex items-center">
              <Search className="absolute left-2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search local content..."
                className="w-[220px] pl-8 bg-black/20 border-gray-700 focus:border-blue-500 text-sm text-white placeholder:text-gray-400"
              />
            </div>
            <Button variant="ghost" size="icon" className="text-gray-300 hover:text-white">
              <Search className="h-5 w-5 md:hidden" />
              <span className="sr-only">Search</span>
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-300 hover:text-white">
              <Bell className="h-5 w-5" />
              <span className="sr-only">Notifications</span>
            </Button>
            <UserNav />
          </div>
        </div>
      </header>

      <main className="container mx-auto">
        {/* Hero Banner */}
        <section className="relative h-[80vh] w-full">
          <Image
            src="/placeholder.svg?height=1080&width=1920"
            fill
            alt="Seychelles Untold"
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/60 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900/90 to-transparent" />
          <div className="absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-center px-4 md:px-6 space-y-4">
            <div className="max-w-lg">
              <Badge className="mb-4 bg-blue-600">Featured Series</Badge>
              <h1 className="text-4xl md:text-6xl font-bold mb-4">Seychelles Untold</h1>
              <p className="text-lg text-gray-200 mb-6">
                Discover the hidden stories of Seychelles in this captivating documentary series. From ancient
                traditions to modern challenges, explore the authentic culture of our islands.
              </p>
              <div className="flex gap-3">
                <Link href="/streaming/watch/seychelles-untold-s1e1" passHref>
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8">
                    <Play className="mr-2 h-5 w-5" /> Play
                  </Button>
                </Link>
                <Link href="/streaming/details/seychelles-untold" passHref>
                  <Button variant="outline" className="bg-gray-800/60 text-white border-gray-600 hover:bg-gray-700">
                    <Info className="mr-2 h-5 w-5" /> More Info
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Genre Tabs */}
        <section className="py-8 px-4 md:px-6">
          <Tabs defaultValue="all" className="w-full">
            <div className="flex justify-between items-center mb-6">
              <TabsList className="bg-gray-800/60">
                <TabsTrigger value="all" className="data-[state=active]:bg-blue-600">
                  All Genres
                </TabsTrigger>
                <TabsTrigger value="documentary" className="data-[state=active]:bg-blue-600">
                  Documentary
                </TabsTrigger>
                <TabsTrigger value="drama" className="data-[state=active]:bg-blue-600">
                  Drama
                </TabsTrigger>
                <TabsTrigger value="comedy" className="data-[state=active]:bg-blue-600">
                  Comedy
                </TabsTrigger>
                <TabsTrigger value="culture" className="data-[state=active]:bg-blue-600">
                  Cultural
                </TabsTrigger>
              </TabsList>
              <Button variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:text-white">
                <Filter className="mr-2 h-4 w-4" /> Filter
              </Button>
            </div>

            <TabsContent value="all" className="mt-0">
              {/* Featured Local Productions */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Featured Local Productions</h2>
                  <Link href="/streaming/featured">
                    <Button variant="ghost" className="text-gray-300 hover:text-white text-sm">
                      See All <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                  {[
                    { id: "seychelles-untold", title: "Seychelles Untold", type: "series", genre: "Documentary" },
                    { id: "island-paradise", title: "Island Paradise", type: "movie", genre: "Documentary" },
                    {
                      id: "coral-reef-chronicles",
                      title: "Coral Reef Chronicles",
                      type: "series",
                      genre: "Documentary",
                    },
                    { id: "creole-kitchen", title: "Creole Kitchen", type: "series", genre: "Cultural" },
                    { id: "mahé-mysteries", title: "Mahé Mysteries", type: "series", genre: "Drama" },
                    { id: "island-life", title: "Island Life", type: "movie", genre: "Comedy" },
                  ].map((item) => (
                    <ContentCard key={item.id} id={item.id} title={item.title} type={item.type} genre={item.genre} />
                  ))}
                </div>
              </div>

              {/* New Releases */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">New Releases</h2>
                  <Link href="/streaming/new">
                    <Button variant="ghost" className="text-gray-300 hover:text-white text-sm">
                      See All <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                  {[
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      type: "movie",
                      genre: "Documentary",
                    },
                    { id: "praslin-stories", title: "Praslin Stories", type: "series", genre: "Drama" },
                    { id: "island-beats", title: "Island Beats", type: "series", genre: "Cultural" },
                    { id: "ocean-guardians", title: "Ocean Guardians", type: "movie", genre: "Documentary" },
                    { id: "creole-comedy-hour", title: "Creole Comedy Hour", type: "series", genre: "Comedy" },
                    { id: "la-digue-diaries", title: "La Digue Diaries", type: "series", genre: "Drama" },
                  ].map((item) => (
                    <ContentCard
                      key={item.id}
                      id={item.id}
                      title={item.title}
                      type={item.type}
                      genre={item.genre}
                      isNew={true}
                    />
                  ))}
                </div>
              </div>

              {/* Popular Series */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Popular Series</h2>
                  <Link href="/streaming/series">
                    <Button variant="ghost" className="text-gray-300 hover:text-white text-sm">
                      See All <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                  {[
                    { id: "seychelles-untold", title: "Seychelles Untold", type: "series", genre: "Documentary" },
                    {
                      id: "coral-reef-chronicles",
                      title: "Coral Reef Chronicles",
                      type: "series",
                      genre: "Documentary",
                    },
                    { id: "creole-kitchen", title: "Creole Kitchen", type: "series", genre: "Cultural" },
                    { id: "mahé-mysteries", title: "Mahé Mysteries", type: "series", genre: "Drama" },
                    { id: "island-beats", title: "Island Beats", type: "series", genre: "Cultural" },
                    { id: "creole-comedy-hour", title: "Creole Comedy Hour", type: "series", genre: "Comedy" },
                  ].map((item) => (
                    <ContentCard key={item.id} id={item.id} title={item.title} type={item.type} genre={item.genre} />
                  ))}
                </div>
              </div>

              {/* Popular Movies */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Popular Movies</h2>
                  <Link href="/streaming/movies">
                    <Button variant="ghost" className="text-gray-300 hover:text-white text-sm">
                      See All <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                  {[
                    { id: "island-paradise", title: "Island Paradise", type: "movie", genre: "Documentary" },
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      type: "movie",
                      genre: "Documentary",
                    },
                    { id: "ocean-guardians", title: "Ocean Guardians", type: "movie", genre: "Documentary" },
                    { id: "island-life", title: "Island Life", type: "movie", genre: "Comedy" },
                    { id: "the-last-fisherman", title: "The Last Fisherman", type: "movie", genre: "Drama" },
                    { id: "festival-of-lights", title: "Festival of Lights", type: "movie", genre: "Cultural" },
                  ].map((item) => (
                    <ContentCard key={item.id} id={item.id} title={item.title} type={item.type} genre={item.genre} />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="documentary" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Documentary</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                  {[
                    { id: "seychelles-untold", title: "Seychelles Untold", type: "series", genre: "Documentary" },
                    { id: "island-paradise", title: "Island Paradise", type: "movie", genre: "Documentary" },
                    {
                      id: "coral-reef-chronicles",
                      title: "Coral Reef Chronicles",
                      type: "series",
                      genre: "Documentary",
                    },
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      type: "movie",
                      genre: "Documentary",
                    },
                    { id: "ocean-guardians", title: "Ocean Guardians", type: "movie", genre: "Documentary" },
                    { id: "hidden-beaches", title: "Hidden Beaches", type: "movie", genre: "Documentary" },
                    { id: "bird-island", title: "Bird Island", type: "series", genre: "Documentary" },
                    { id: "marine-life", title: "Marine Life of Seychelles", type: "series", genre: "Documentary" },
                    { id: "aldabra-atoll", title: "Aldabra Atoll", type: "movie", genre: "Documentary" },
                    { id: "conservation-heroes", title: "Conservation Heroes", type: "series", genre: "Documentary" },
                    { id: "island-ecology", title: "Island Ecology", type: "series", genre: "Documentary" },
                    { id: "traditional-fishing", title: "Traditional Fishing", type: "movie", genre: "Documentary" },
                  ].map((item) => (
                    <ContentCard key={item.id} id={item.id} title={item.title} type={item.type} genre={item.genre} />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="drama" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Drama</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                  {[
                    { id: "mahé-mysteries", title: "Mahé Mysteries", type: "series", genre: "Drama" },
                    { id: "praslin-stories", title: "Praslin Stories", type: "series", genre: "Drama" },
                    { id: "la-digue-diaries", title: "La Digue Diaries", type: "series", genre: "Drama" },
                    { id: "the-last-fisherman", title: "The Last Fisherman", type: "movie", genre: "Drama" },
                    { id: "island-dreams", title: "Island Dreams", type: "movie", genre: "Drama" },
                    { id: "family-ties", title: "Family Ties", type: "series", genre: "Drama" },
                    { id: "the-return", title: "The Return", type: "movie", genre: "Drama" },
                    { id: "lost-heritage", title: "Lost Heritage", type: "series", genre: "Drama" },
                  ].map((item) => (
                    <ContentCard key={item.id} id={item.id} title={item.title} type={item.type} genre={item.genre} />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="comedy" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Comedy</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                  {[
                    { id: "island-life", title: "Island Life", type: "movie", genre: "Comedy" },
                    { id: "creole-comedy-hour", title: "Creole Comedy Hour", type: "series", genre: "Comedy" },
                    { id: "beach-buddies", title: "Beach Buddies", type: "series", genre: "Comedy" },
                    { id: "tourist-troubles", title: "Tourist Troubles", type: "movie", genre: "Comedy" },
                    { id: "island-mishaps", title: "Island Mishaps", type: "series", genre: "Comedy" },
                    { id: "hotel-hilarity", title: "Hotel Hilarity", type: "series", genre: "Comedy" },
                  ].map((item) => (
                    <ContentCard key={item.id} id={item.id} title={item.title} type={item.type} genre={item.genre} />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="culture" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Cultural</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                  {[
                    { id: "creole-kitchen", title: "Creole Kitchen", type: "series", genre: "Cultural" },
                    { id: "island-beats", title: "Island Beats", type: "series", genre: "Cultural" },
                    { id: "festival-of-lights", title: "Festival of Lights", type: "movie", genre: "Cultural" },
                    { id: "traditional-dance", title: "Traditional Dance", type: "series", genre: "Cultural" },
                    { id: "creole-heritage", title: "Creole Heritage", type: "movie", genre: "Cultural" },
                    { id: "island-crafts", title: "Island Crafts", type: "series", genre: "Cultural" },
                    { id: "seychelles-music", title: "Seychelles Music", type: "series", genre: "Cultural" },
                    { id: "local-legends", title: "Local Legends", type: "movie", genre: "Cultural" },
                  ].map((item) => (
                    <ContentCard key={item.id} id={item.id} title={item.title} type={item.type} genre={item.genre} />
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-800 py-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-400">Seychelles' premier streaming platform for local content</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Browse</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/streaming/series" className="text-sm text-gray-400 hover:text-white">
                    Series
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/movies" className="text-sm text-gray-400 hover:text-white">
                    Movies
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/new" className="text-sm text-gray-400 hover:text-white">
                    New & Popular
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/mylist" className="text-sm text-gray-400 hover:text-white">
                    My List
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Help</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Account
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Devices
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Terms of Use
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Cookie Preferences
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Corporate Information
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800 text-center">
            <p className="text-xs text-gray-400">
              &copy; {new Date().getFullYear()} Kominote Streaming. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="streaming" colorScheme="blue" />

      {/* Messenger - with dark variant for visibility on dark backgrounds */}
      <Messenger variant="dark" />
    </div>
  )
}

// Content Card Component
function ContentCard({ id, title, type, genre, isNew = false }) {
  return (
    <div className="group relative overflow-hidden rounded-md aspect-video cursor-pointer transition-transform duration-200 hover:scale-105 hover:z-10">
      <Image
        src="/placeholder.svg?height=180&width=320"
        width={320}
        height={180}
        alt={title}
        className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-110"
      />
      {isNew && <Badge className="absolute top-2 left-2 bg-blue-600">New</Badge>}
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute bottom-0 left-0 right-0 p-3 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
        <div className="flex items-center gap-2">
          <Button
            size="icon"
            className="h-8 w-8 rounded-full bg-blue-600 hover:bg-blue-700"
            onClick={(e) => {
              e.stopPropagation()
              window.location.href = `/streaming/watch/${id}`
            }}
          >
            <Play className="h-4 w-4" />
          </Button>
          <Button
            size="icon"
            variant="ghost"
            className="h-8 w-8 rounded-full bg-gray-800/60 hover:bg-gray-700/80"
            onClick={(e) => {
              e.stopPropagation()
              window.location.href = `/streaming/details/${id}`
            }}
          >
            <Info className="h-4 w-4" />
          </Button>
        </div>
        <h3 className="mt-2 text-sm font-medium line-clamp-1">{title}</h3>
        <div className="flex items-center gap-2 mt-1">
          <Badge variant="outline" className="text-[10px] border-gray-600 text-gray-400 px-1">
            {type === "series" ? "SERIES" : "MOVIE"}
          </Badge>
          <span className="text-xs text-gray-400">{genre}</span>
        </div>
      </div>
      {/* Clickable overlay for the entire card */}
      <Link href={`/streaming/details/${id}`} className="absolute inset-0">
        <span className="sr-only">View details for {title}</span>
      </Link>
    </div>
  )
}

