# Django
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
db.sqlite3
db.sqlite3-journal

# Environment variables
.env
.env.local
.env.production

# Virtual environment
kominote_env/
venv/
env/
ENV/

# Static files
staticfiles/
static_collected/

# Media files (user uploads)
media/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing
.coverage
.pytest_cache/
htmlcov/
.tox/
.nox/

# Documentation
docs/_build/

# Backup files
*.bak
*.backup

# Logs
logs/
*.log

# Temporary files
*.tmp
*.temp

# Database
*.db
*.sqlite
*.sqlite3

# Secrets
secrets.json
.secrets/
