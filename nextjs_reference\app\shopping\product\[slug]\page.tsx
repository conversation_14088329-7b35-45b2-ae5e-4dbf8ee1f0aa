import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  Star,
  Heart,
  Share2,
  ShoppingCart,
  Truck,
  Shield,
  RefreshCw,
  ChevronRight,
  MessageSquare,
  Store,
  Check,
  MapPin,
  Clock,
  TrendingUp,
} from "lucide-react"
import FeatureNavigation from "@/components/feature-navigation"
import { Messenger } from "@/components/messenger"

export default function ProductDetailPage({ params }: { params: { slug: string } }) {
  // In a real app, you would fetch product data based on the slug
  const product = {
    id: "coconut-bowl-01",
    name: "Handmade Coconut Bowl",
    slug: params.slug,
    price: 24.99,
    originalPrice: 34.99,
    discount: 29,
    rating: 4.8,
    reviews: 24,
    inStock: true,
    description:
      "Handcrafted from real coconut shells, these beautiful bowls are perfect for serving açaí bowls, smoothies, salads, or as a decorative piece. Each bowl is unique with its own natural pattern and color variations.",
    features: [
      "100% natural coconut shell",
      "Eco-friendly and sustainable",
      "Food-safe coating",
      "Handcrafted by local artisans",
      "Supports Seychellois craftsmanship",
    ],
    specifications: {
      material: "Natural coconut shell",
      dimensions: "Approximately 12-14cm diameter, 6-7cm height",
      capacity: "400-500ml",
      care: "Hand wash with mild soap, not dishwasher safe",
      origin: "Handmade in Seychelles",
    },
    images: [
      "/placeholder.svg?height=600&width=600",
      "/placeholder.svg?height=600&width=600",
      "/placeholder.svg?height=600&width=600",
      "/placeholder.svg?height=600&width=600",
    ],
    categories: ["Home & Kitchen", "Eco-Friendly", "Local Crafts"],
    tags: ["coconut", "bowl", "handmade", "eco-friendly", "kitchen"],
    vendor: {
      id: "island-crafts",
      name: "Island Crafts Co.",
      rating: 4.9,
      reviews: 48,
      products: 124,
      location: "Victoria, Mahé",
      joined: "2 years ago",
      sales: "500+",
      avatar: "/placeholder.svg?height=80&width=80",
    },
    relatedProducts: [
      {
        id: "coconut-spoon-01",
        name: "Coconut Shell Spoon Set",
        price: 18.99,
        image: "/placeholder.svg?height=300&width=300",
        rating: 4.7,
        reviews: 18,
      },
      {
        id: "bamboo-straw-01",
        name: "Reusable Bamboo Straws",
        price: 12.99,
        image: "/placeholder.svg?height=300&width=300",
        rating: 4.6,
        reviews: 32,
      },
      {
        id: "palm-plate-01",
        name: "Palm Leaf Plates (Set of 4)",
        price: 29.99,
        image: "/placeholder.svg?height=300&width=300",
        rating: 4.8,
        reviews: 15,
      },
      {
        id: "seashell-decor-01",
        name: "Seashell Wall Decoration",
        price: 34.99,
        image: "/placeholder.svg?height=300&width=300",
        rating: 4.5,
        reviews: 9,
      },
    ],
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Simplified for this page */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-emerald-600">
              <Heart className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-emerald-600 relative">
              <ShoppingCart className="h-5 w-5" />
              <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-emerald-600">
                3
              </Badge>
            </Button>
          </div>
        </div>
      </header>

      <main className="container px-4 py-8 md:px-6">
        {/* Breadcrumb */}
        <div className="flex items-center text-sm text-gray-500 mb-6">
          <Link href="/shopping" className="hover:text-emerald-600">
            Home
          </Link>
          <ChevronRight className="h-4 w-4 mx-2" />
          <Link href="/shopping/category/home-kitchen" className="hover:text-emerald-600">
            Home & Kitchen
          </Link>
          <ChevronRight className="h-4 w-4 mx-2" />
          <span className="text-emerald-600 font-medium">{product.name}</span>
        </div>

        {/* Product Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative aspect-square rounded-lg overflow-hidden border bg-white">
              <Image
                src={product.images[0] || "/placeholder.svg"}
                alt={product.name}
                fill
                className="object-contain p-4"
              />
            </div>
            <div className="grid grid-cols-4 gap-2">
              {product.images.map((image, index) => (
                <div
                  key={index}
                  className={`relative aspect-square rounded-md overflow-hidden border cursor-pointer ${index === 0 ? "ring-2 ring-emerald-500" : ""}`}
                >
                  <Image
                    src={image || "/placeholder.svg"}
                    alt={`${product.name} - Image ${index + 1}`}
                    fill
                    className="object-contain p-2"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-2">
                {product.inStock ? (
                  <Badge className="bg-green-500">In Stock</Badge>
                ) : (
                  <Badge variant="outline" className="text-red-500 border-red-200">
                    Out of Stock
                  </Badge>
                )}
                {product.discount > 0 && <Badge className="bg-red-500">Sale</Badge>}
              </div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
              <div className="flex items-center gap-2 mb-4">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${i < Math.floor(product.rating) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}`}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-500">
                  {product.rating} ({product.reviews} reviews)
                </span>
              </div>
              <div className="flex items-center gap-3 mb-4">
                {product.discount > 0 && (
                  <span className="text-gray-500 line-through text-sm">${product.originalPrice.toFixed(2)}</span>
                )}
                <span className="text-2xl font-bold text-emerald-600">${product.price.toFixed(2)}</span>
                {product.discount > 0 && (
                  <Badge className="bg-red-100 text-red-600 hover:bg-red-100">{product.discount}% OFF</Badge>
                )}
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-500 mb-6">
                <Link
                  href={`/shopping/vendor/${product.vendor.id}`}
                  className="flex items-center gap-1 hover:text-emerald-600"
                >
                  <Store className="h-4 w-4" />
                  <span>{product.vendor.name}</span>
                </Link>
                <span>•</span>
                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                  <span>
                    {product.vendor.rating} ({product.vendor.reviews} reviews)
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <p className="text-gray-600">{product.description}</p>
              <ul className="space-y-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <Check className="h-5 w-5 text-emerald-500 shrink-0 mt-0.5" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="pt-6 border-t border-gray-200 space-y-4">
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="flex-1">
                  <Button className="w-full bg-emerald-600 hover:bg-emerald-700">
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Add to Cart
                  </Button>
                </div>
                <Button variant="outline" size="icon" className="border-gray-300">
                  <Heart className="h-5 w-5 text-gray-600" />
                </Button>
                <Button variant="outline" size="icon" className="border-gray-300">
                  <Share2 className="h-5 w-5 text-gray-600" />
                </Button>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button variant="outline" className="flex-1 border-emerald-200 text-emerald-600 hover:bg-emerald-50">
                  Buy Now
                </Button>
                <Button variant="outline" className="flex-1 border-emerald-200 text-emerald-600 hover:bg-emerald-50">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Contact Vendor
                </Button>
              </div>
            </div>

            <div className="pt-6 border-t border-gray-200 grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                  <Truck className="h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Free Delivery</p>
                  <p className="text-xs text-gray-500">For orders over $50</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                  <Shield className="h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Secure Payment</p>
                  <p className="text-xs text-gray-500">100% secure checkout</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                  <RefreshCw className="h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Easy Returns</p>
                  <p className="text-xs text-gray-500">30 day return policy</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mb-12">
          <Tabs defaultValue="details">
            <TabsList className="grid w-full grid-cols-3 md:w-[400px]">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="specifications">Specifications</TabsTrigger>
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
            </TabsList>
            <TabsContent value="details" className="p-6 border rounded-md mt-4 bg-white">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Product Description</h3>
                <p className="text-gray-600">{product.description}</p>
                <p className="text-gray-600">
                  Each coconut bowl is carefully selected, cleaned, and sanded to create a smooth finish. The bowls are
                  then polished with coconut oil to enhance their natural beauty and treated with a food-safe sealant to
                  ensure durability.
                </p>
                <p className="text-gray-600">
                  These versatile bowls are perfect for serving açaí bowls, smoothies, salads, or as a decorative piece.
                  They add a tropical touch to your table and are a conversation starter at any gathering.
                </p>
                <h3 className="text-lg font-medium pt-4">Features</h3>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-emerald-500 shrink-0 mt-0.5" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </TabsContent>
            <TabsContent value="specifications" className="p-6 border rounded-md mt-4 bg-white">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Product Specifications</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(product.specifications).map(([key, value]) => (
                    <div key={key} className="border-b pb-2">
                      <p className="text-sm text-gray-500 capitalize">{key}</p>
                      <p className="font-medium">{value}</p>
                    </div>
                  ))}
                </div>
                <div className="pt-4">
                  <h4 className="text-base font-medium mb-2">Care Instructions</h4>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-emerald-500 shrink-0 mt-0.5" />
                      <span>Hand wash with mild soap and warm water</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-emerald-500 shrink-0 mt-0.5" />
                      <span>Do not use in microwave or dishwasher</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-emerald-500 shrink-0 mt-0.5" />
                      <span>Avoid soaking for extended periods</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="h-5 w-5 text-emerald-500 shrink-0 mt-0.5" />
                      <span>Occasionally treat with coconut oil to maintain finish</span>
                    </li>
                  </ul>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="reviews" className="p-6 border rounded-md mt-4 bg-white">
              <div className="space-y-6">
                <div className="flex flex-col md:flex-row gap-6 md:items-center">
                  <div className="md:w-1/3 flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-5xl font-bold text-emerald-600 mb-2">{product.rating}</div>
                    <div className="flex mb-2">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-5 w-5 ${i < Math.floor(product.rating) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}`}
                        />
                      ))}
                    </div>
                    <p className="text-sm text-gray-500">Based on {product.reviews} reviews</p>
                  </div>
                  <div className="md:w-2/3 space-y-3">
                    {[5, 4, 3, 2, 1].map((star) => (
                      <div key={star} className="flex items-center gap-4">
                        <div className="flex items-center w-20">
                          <span className="text-sm text-gray-600 mr-2">{star}</span>
                          <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                        </div>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-yellow-400 rounded-full"
                            style={{
                              width: `${star === 5 ? 70 : star === 4 ? 20 : star === 3 ? 7 : star === 2 ? 2 : 1}%`,
                            }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-500 w-12">
                          {star === 5 ? "70%" : star === 4 ? "20%" : star === 3 ? "7%" : star === 2 ? "2%" : "1%"}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="border-t pt-6">
                  <h3 className="text-lg font-medium mb-4">Customer Reviews</h3>
                  <div className="space-y-6">
                    {[
                      {
                        name: "Marie Dubois",
                        avatar: "/placeholder.svg?height=40&width=40",
                        rating: 5,
                        date: "2 weeks ago",
                        comment:
                          "These coconut bowls are absolutely beautiful! Each one is unique and they make my morning smoothie bowls feel extra special. The quality is excellent and they're exactly as described.",
                      },
                      {
                        name: "Jean Baptiste",
                        avatar: "/placeholder.svg?height=40&width=40",
                        rating: 4,
                        date: "1 month ago",
                        comment:
                          "Great product that feels authentic and well-made. I bought a set of 4 and they've been perfect for serving snacks when guests come over. Everyone asks where I got them!",
                      },
                      {
                        name: "Sophia Laurent",
                        avatar: "/placeholder.svg?height=40&width=40",
                        rating: 5,
                        date: "2 months ago",
                        comment:
                          "I love supporting local artisans, and these bowls are a wonderful example of Seychellois craftsmanship. They're beautiful, functional, and sustainable. Highly recommend!",
                      },
                    ].map((review, index) => (
                      <div key={index} className="border-b pb-6 last:border-0 last:pb-0">
                        <div className="flex items-start gap-4">
                          <Image
                            src={review.avatar || "/placeholder.svg"}
                            alt={review.name}
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                          <div className="flex-1">
                            <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                              <h4 className="font-medium">{review.name}</h4>
                              <div className="flex sm:ml-auto">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-4 w-4 ${i < review.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}`}
                                  />
                                ))}
                              </div>
                            </div>
                            <p className="text-sm text-gray-500 mb-2">{review.date}</p>
                            <p className="text-gray-600">{review.comment}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-6 flex justify-center">
                    <Button variant="outline" className="border-emerald-200 text-emerald-600 hover:bg-emerald-50">
                      Load More Reviews
                    </Button>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Vendor Information */}
        <div className="mb-12 bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="p-6 md:p-8 flex flex-col justify-center">
              <div className="flex items-center gap-4 mb-4">
                <div className="h-16 w-16 rounded-full bg-emerald-100 flex items-center justify-center">
                  <Store className="h-8 w-8 text-emerald-600" />
                </div>
                <div>
                  <h3 className="font-bold text-xl">{product.vendor.name}</h3>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                    <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                    <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                    <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                    <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                    <span className="text-sm text-gray-500 ml-1">({product.vendor.reviews} reviews)</span>
                  </div>
                </div>
              </div>
              <p className="text-gray-600 mb-6">
                Island Crafts Co. specializes in handmade Seychellois crafts using sustainable materials. Each piece
                tells a story of our island heritage and supports local artisans.
              </p>
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-emerald-600" />
                  <span className="text-sm">{product.vendor.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-emerald-600" />
                  <span className="text-sm">Joined {product.vendor.joined}</span>
                </div>
                <div className="flex items-center gap-2">
                  <ShoppingCart className="h-4 w-4 text-emerald-600" />
                  <span className="text-sm">{product.vendor.products} Products</span>
                </div>
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-emerald-600" />
                  <span className="text-sm">{product.vendor.sales} Sales</span>
                </div>
              </div>
              <div className="flex gap-3">
                <Button className="bg-emerald-600 hover:bg-emerald-700 text-white">Visit Store</Button>
                <Button variant="outline" className="border-emerald-200 text-emerald-600 hover:bg-emerald-50">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Contact
                </Button>
              </div>
            </div>
            <div className="relative h-[300px] md:h-auto">
              <Image src="/placeholder.svg?height=400&width=600" fill alt="Vendor banner" className="object-cover" />
            </div>
          </div>
        </div>

        {/* Related Products */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6">You May Also Like</h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 md:gap-6">
            {product.relatedProducts.map((relatedProduct, index) => (
              <Link key={index} href={`/shopping/product/${relatedProduct.id}`} className="group">
                <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md">
                  <div className="relative">
                    <div className="aspect-square overflow-hidden">
                      <Image
                        src={relatedProduct.image || "/placeholder.svg"}
                        width={300}
                        height={300}
                        alt={relatedProduct.name}
                        className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute top-2 right-2 h-8 w-8 rounded-full bg-white/80 text-gray-600 hover:text-emerald-600 hover:bg-white"
                    >
                      <Heart className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-3">
                    <div className="flex items-center gap-1 mb-1">
                      <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                      <span className="text-xs text-gray-500">
                        {relatedProduct.rating} ({relatedProduct.reviews})
                      </span>
                    </div>
                    <h3 className="font-medium text-gray-800 line-clamp-1 group-hover:text-emerald-600 transition-colors">
                      {relatedProduct.name}
                    </h3>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-sm font-bold text-emerald-600">${relatedProduct.price.toFixed(2)}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 rounded-full bg-emerald-100 text-emerald-600 hover:bg-emerald-200"
                      >
                        <ShoppingCart className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Recently Viewed */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Recently Viewed</h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {[
              {
                name: "Handmade Coconut Bowl",
                price: 24.99,
                image: "/placeholder.svg?height=200&width=200",
              },
              {
                name: "Coconut Shell Spoon Set",
                price: 18.99,
                image: "/placeholder.svg?height=200&width=200",
              },
              {
                name: "Palm Leaf Plates",
                price: 29.99,
                image: "/placeholder.svg?height=200&width=200",
              },
              {
                name: "Seashell Wall Decoration",
                price: 34.99,
                image: "/placeholder.svg?height=200&width=200",
              },
              {
                name: "Bamboo Cutlery Set",
                price: 22.5,
                image: "/placeholder.svg?height=200&width=200",
              },
              {
                name: "Woven Beach Bag",
                price: 45.0,
                image: "/placeholder.svg?height=200&width=200",
              },
            ].map((product, index) => (
              <Link
                href={`/shopping/product/${product.name.toLowerCase().replace(/\s+/g, "-")}`}
                key={index}
                className="group"
              >
                <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md">
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      width={200}
                      height={200}
                      alt={product.name}
                      className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="p-3">
                    <h3 className="font-medium text-gray-800 line-clamp-1 group-hover:text-emerald-600 transition-colors text-sm">
                      {product.name}
                    </h3>
                    <span className="text-sm font-bold text-emerald-600">${product.price.toFixed(2)}</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </main>

      {/* Footer - Simplified for this page */}
      <footer className="bg-white border-t py-8">
        <div className="container px-4 md:px-6">
          <div className="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Shopping. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Terms of Service
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Privacy Policy
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="shopping" colorScheme="emerald" />

      {/* Messenger */}
      <Messenger variant="light" />
    </div>
  )
}

