# ===============================================================================
# DJANGO DEVELOPMENT WORKFLOW AND BACKUP STRATEGY
# ===============================================================================
# 
# A comprehensive, reusable workflow for Django project development with
# enterprise-level version control and backup strategies.
# 
# Author: Finessed
# Created: 2025
# Purpose: Template for all Django projects with safe development practices
# 
# ===============================================================================

# ===============================================================================
# 1. PROJECT INITIALIZATION WORKFLOW
# ===============================================================================

## 1.1 Environment Setup
# Create project directory
mkdir project_name
cd project_name

# Create virtual environment
python -m venv project_env
project_env\Scripts\activate  # Windows
# source project_env/bin/activate  # Linux/Mac

# Upgrade pip
python -m pip install --upgrade pip

## 1.2 Django Project Creation
# Install Django and core dependencies
pip install Django>=4.2
pip install djangorestframework
pip install psycopg2-binary
pip install django-cors-headers
pip install djangorestframework-simplejwt
pip install python-decouple
pip install Pillow
pip install django-extensions
pip install django-debug-toolbar
pip install drf-spectacular
pip install django-admin-interface

# Create requirements.txt
pip freeze > requirements.txt

# Create Django project
django-admin startproject project_name .

## 1.3 Initial Configuration
# Create .env file (NEVER commit to Git)
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
DB_NAME=project_db
DB_USER=project_user
DB_PASSWORD=project_password
DB_HOST=localhost
DB_PORT=5432
USE_SQLITE=True

# Create .gitignore
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
db.sqlite3
db.sqlite3-journal
.env
.env.local
.env.production
project_env/
venv/
env/
staticfiles/
media/
.vscode/
.idea/
.DS_Store
Thumbs.db

# ===============================================================================
# 2. GIT VERSION CONTROL SETUP
# ===============================================================================

## 2.1 Git Initialization
git init
git config user.name "Your Name"
git config user.email "<EMAIL>"

## 2.2 Remote Repository Setup
# Create private repository on GitHub
git remote add origin https://github.com/username/project-name.git

## 2.3 Initial Commit
git add .
git commit -m "Initial commit: Django project setup"
git branch -M main
git push -u origin main

# ===============================================================================
# 3. PHASE-BASED DEVELOPMENT WORKFLOW
# ===============================================================================

## 3.1 Phase Structure Template
# Phase 1: Project Setup & Infrastructure
# Phase 2: Django Apps Creation
# Phase 3: Database Models Design
# Phase 4: API Development
# Phase 5: Frontend Templates
# Phase 6: Integration & Testing
# Phase 7: Deployment Preparation

## 3.2 Phase Development Workflow

### Starting a New Phase:
# Create backup of previous phase
git checkout -b phase-X-complete  # Where X is previous phase number
git push origin phase-X-complete

# Create tag for previous phase
git tag -a vX.0-phaseX -m "Phase X Complete: Description"
git push origin vX.0-phaseX

# Create new development branch
git checkout main
git checkout -b phase-Y-description  # Where Y is new phase number
git push origin phase-Y-description

### During Phase Development:
# Regular commits with descriptive messages
git add .
git commit -m "phaseY: Specific change description"
git push origin phase-Y-description

# Test frequently
python manage.py check
python manage.py test
python manage.py runserver

### Completing a Phase:
# Final testing
python manage.py check
python manage.py test
python manage.py collectstatic --noinput

# Merge to main
git checkout main
git merge phase-Y-description
git tag -a vY.0-phaseY -m "Phase Y Complete: Description"
git push origin main
git push origin vY.0-phaseY

# ===============================================================================
# 4. BACKUP STRATEGY
# ===============================================================================

## 4.1 Git Backup Levels
# Level 1: Local Git repository
# Level 2: Remote GitHub repository (primary backup)
# Level 3: Phase branches (rollback points)
# Level 4: Tagged releases (exact rollback points)

## 4.2 Backup Frequency
# After each significant change: git commit + git push
# After each feature completion: git push origin branch-name
# After each phase completion: git tag + git push origin tag-name
# Daily: Ensure all work is pushed to GitHub

## 4.3 Rollback Strategies

### Rollback to Previous Phase:
git checkout phase-X-complete
git checkout -b phase-Y-attempt-2

### Rollback to Specific Tag:
git checkout vX.0-phaseX
git checkout -b new-development-branch

### Emergency Recovery:
git clone https://github.com/username/project-name.git
cd project-name
git checkout phase-X-complete  # Go to last stable state

# ===============================================================================
# 5. DJANGO DEVELOPMENT BEST PRACTICES
# ===============================================================================

## 5.1 App Creation Workflow
# Create app in apps directory
mkdir apps
python manage.py startapp app_name apps/app_name

# Update settings.py INSTALLED_APPS
'apps.app_name',

# Create app URLs
# apps/app_name/urls.py
from django.urls import path
from . import views

app_name = 'app_name'
urlpatterns = [
    path('', views.index, name='index'),
]

# Include in main URLs
# project_name/urls.py
path('app_name/', include('apps.app_name.urls')),

## 5.2 Database Workflow
# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

## 5.3 Testing Workflow
# Run tests
python manage.py test

# Run specific app tests
python manage.py test apps.app_name

# Check for issues
python manage.py check

## 5.4 Static Files Workflow
# Collect static files
python manage.py collectstatic

# Development server
python manage.py runserver

# ===============================================================================
# 6. COMMIT MESSAGE CONVENTIONS
# ===============================================================================

## 6.1 Commit Types
feat:     # New feature
fix:      # Bug fix
docs:     # Documentation changes
style:    # Code style changes (formatting, etc.)
refactor: # Code refactoring
test:     # Adding or updating tests
chore:    # Maintenance tasks
phase1:   # Phase 1 specific work
phase2:   # Phase 2 specific work
...

## 6.2 Commit Message Examples
git commit -m "feat: Add user authentication system"
git commit -m "fix: Resolve CORS issue for mobile apps"
git commit -m "docs: Update API documentation"
git commit -m "phase2: Create accounts app structure"
git commit -m "🎉 Phase 2 Complete: Django Apps Creation"

# ===============================================================================
# 7. SECURITY BEST PRACTICES
# ===============================================================================

## 7.1 Environment Variables
# Always use .env for sensitive data
# Never commit .env files to Git
# Use python-decouple for configuration management

## 7.2 Git Security
# Keep repositories private for proprietary code
# Use .gitignore to exclude sensitive files
# Regular security audits of dependencies

## 7.3 Django Security
# Use strong SECRET_KEY
# Set DEBUG=False in production
# Configure ALLOWED_HOSTS properly
# Use HTTPS in production
# Regular dependency updates

# ===============================================================================
# 8. EMERGENCY PROCEDURES
# ===============================================================================

## 8.1 If Development Goes Wrong
# Stop current work
# Checkout last stable phase
git checkout phase-X-complete

# Create new attempt branch
git checkout -b phase-Y-attempt-2

# Start fresh with lessons learned

## 8.2 If Repository Gets Corrupted
# Clone fresh from GitHub
git clone https://github.com/username/project-name.git

# Recreate virtual environment
python -m venv project_env
project_env\Scripts\activate
pip install -r requirements.txt

# Recreate .env file (from secure backup)
# Run migrations
python manage.py migrate

## 8.3 If GitHub Repository Issues
# Create new repository
# Push all branches and tags
git push new-origin --all
git push new-origin --tags

# ===============================================================================
# 9. DEPLOYMENT PREPARATION
# ===============================================================================

## 9.1 Pre-Deployment Checklist
# [ ] All tests passing
# [ ] DEBUG=False in production settings
# [ ] Static files collected
# [ ] Database migrations applied
# [ ] Environment variables configured
# [ ] Security settings reviewed
# [ ] Dependencies updated and tested

## 9.2 Production Branch Strategy
# Create production branch
git checkout -b production
git push origin production

# Tag production releases
git tag -a v1.0.0 -m "Production Release 1.0.0"
git push origin v1.0.0

# ===============================================================================
# 10. TEAM COLLABORATION WORKFLOW
# ===============================================================================

## 10.1 Branch Naming Conventions
main                    # Production-ready code
phase-X-description     # Phase development branches
feature/feature-name    # Individual features
bugfix/issue-name      # Bug fixes
hotfix/critical-fix    # Critical production fixes

## 10.2 Pull Request Workflow
# Create feature branch
git checkout -b feature/new-feature

# Develop and commit
git add .
git commit -m "feat: Add new feature"
git push origin feature/new-feature

# Create pull request on GitHub
# Review and merge after approval

# ===============================================================================
# 11. MONITORING AND MAINTENANCE
# ===============================================================================

## 11.1 Regular Maintenance Tasks
# Update dependencies monthly
pip list --outdated
pip install --upgrade package-name

# Security audits
pip audit

# Clean up old branches
git branch -d old-branch-name
git push origin --delete old-branch-name

## 11.2 Backup Verification
# Verify GitHub backup weekly
git remote -v
git branch -a
git tag -l

# Test rollback procedures quarterly
git checkout phase-X-complete
# Verify functionality

# ===============================================================================
# 12. QUICK REFERENCE COMMANDS
# ===============================================================================

## 12.1 Daily Development
git status                          # Check current status
git add .                           # Stage changes
git commit -m "description"         # Commit changes
git push origin branch-name         # Push to GitHub

## 12.2 Branch Management
git branch -a                       # List all branches
git checkout branch-name            # Switch branches
git checkout -b new-branch          # Create new branch
git merge branch-name               # Merge branch

## 12.3 Rollback Commands
git checkout phase-X-complete       # Go to phase backup
git checkout vX.0-phaseX           # Go to tagged version
git reset --hard commit-hash        # Reset to specific commit

## 12.4 Django Commands
python manage.py runserver          # Start development server
python manage.py makemigrations     # Create migrations
python manage.py migrate            # Apply migrations
python manage.py test               # Run tests
python manage.py check              # Check for issues
python manage.py collectstatic      # Collect static files

# ===============================================================================
# END OF WORKFLOW DOCUMENT
# ===============================================================================
# 
# This workflow provides enterprise-level development practices with:
# - Safe phase-based development
# - Comprehensive backup strategies
# - Professional version control
# - Security best practices
# - Emergency recovery procedures
# 
# Reuse this template for all Django projects to ensure consistent,
# professional development practices.
# 
# ===============================================================================
