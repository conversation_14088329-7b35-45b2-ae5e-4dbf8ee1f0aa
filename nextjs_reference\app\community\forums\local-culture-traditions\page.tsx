import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  Users,
  MessageSquare,
  Bell,
  ChevronDown,
  Filter,
  Plus,
  Globe,
  Pin,
  Eye,
  Clock,
  MessageCircle,
} from "lucide-react"
import FeatureNavigation from "@/components/feature-navigation"

export default function LocalCultureForumPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <div className="hidden md:flex relative w-full max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search discussions, groups, or users..."
                className="pl-10 pr-4 py-2 w-full border-purple-200 focus:border-purple-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-purple-600 relative">
              <Bell className="h-5 w-5" />
              <span className="sr-only">Notifications</span>
              <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-purple-600">
                5
              </Badge>
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-purple-600 relative">
              <MessageSquare className="h-5 w-5" />
              <span className="sr-only">Messages</span>
              <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-purple-600">
                3
              </Badge>
            </Button>
            <div className="flex items-center gap-2">
              <Button variant="ghost" className="text-gray-600 hover:text-purple-600">
                <div className="h-8 w-8 rounded-full bg-purple-100 mr-2 overflow-hidden">
                  <Image
                    src="/placeholder.svg?height=32&width=32"
                    width={32}
                    height={32}
                    alt="Profile"
                    className="object-cover"
                  />
                </div>
                <span className="hidden md:inline">Sarah Johnson</span>
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>
        <div className="container px-4 py-2 border-t border-gray-100">
          <nav className="flex items-center gap-6 overflow-x-auto pb-2 md:pb-0">
            <Link
              href="/community"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Home
            </Link>
            <Link
              href="/community/forums"
              className="text-sm font-medium whitespace-nowrap text-purple-600 border-b-2 border-purple-600 pb-1"
            >
              Forums
            </Link>
            <Link
              href="/community/groups"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Groups
            </Link>
            <Link
              href="/community/members"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Members
            </Link>
            <Link
              href="/community/activity"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Activity
            </Link>
          </nav>
        </div>
      </header>

      <main>
        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="container px-4 py-3">
            <div className="flex items-center text-sm text-gray-500">
              <Link href="/community" className="hover:text-purple-600">
                Community
              </Link>
              <span className="mx-2">/</span>
              <Link href="/community/forums" className="hover:text-purple-600">
                Forums
              </Link>
              <span className="mx-2">/</span>
              <span className="text-purple-600 font-medium">Local Culture & Traditions</span>
            </div>
          </div>
        </div>

        {/* Forum Header */}
        <section className="bg-white py-8 border-b">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div className="flex items-start gap-4">
                <div className="h-16 w-16 rounded-full bg-purple-100 flex items-center justify-center shrink-0">
                  <Globe className="h-8 w-8 text-purple-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold mb-2">Local Culture & Traditions</h1>
                  <p className="text-gray-600 max-w-2xl">
                    Discuss Seychellois culture, traditions, language, and heritage. Share stories, ask questions, and
                    learn about the rich cultural tapestry of our islands.
                  </p>
                  <div className="flex items-center gap-4 mt-3">
                    <div className="flex items-center gap-1 text-sm text-gray-500">
                      <Users className="h-4 w-4" />
                      <span>1,243 members</span>
                    </div>
                    <div className="flex items-center gap-1 text-sm text-gray-500">
                      <MessageSquare className="h-4 w-4" />
                      <span>156 topics</span>
                    </div>
                    <div className="flex items-center gap-1 text-sm text-gray-500">
                      <MessageCircle className="h-4 w-4" />
                      <span>2,487 posts</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button className="bg-purple-600 hover:bg-purple-700 gap-2">
                  <Plus className="h-4 w-4" /> New Topic
                </Button>
                <Button variant="outline" className="border-purple-200 text-purple-600 hover:bg-purple-50">
                  Join Forum
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Forum Content */}
        <section className="py-8">
          <div className="container px-4 md:px-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Left Sidebar */}
              <div className="lg:col-span-1 space-y-6">
                {/* Search */}
                <div className="bg-white rounded-lg shadow-sm p-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="search"
                      placeholder="Search this forum..."
                      className="pl-10 pr-4 py-2 w-full border-gray-200 focus:border-purple-500"
                    />
                  </div>
                </div>

                {/* Forum Categories */}
                <div className="bg-white rounded-lg shadow-sm p-4">
                  <h3 className="font-bold text-lg mb-4">Categories</h3>
                  <div className="space-y-2">
                    {[
                      { name: "Creole Language", count: 42 },
                      { name: "Traditional Music & Dance", count: 38 },
                      { name: "Local Cuisine", count: 56 },
                      { name: "Festivals & Celebrations", count: 29 },
                      { name: "Folklore & Stories", count: 24 },
                      { name: "Arts & Crafts", count: 31 },
                      { name: "Historical Discussions", count: 18 },
                    ].map((category, index) => (
                      <Link
                        href={`/community/forums/local-culture-traditions/${category.name.toLowerCase().replace(/\s+/g, "-")}`}
                        key={index}
                        className="flex items-center justify-between py-2 px-3 rounded-md hover:bg-purple-50 transition-colors"
                      >
                        <span className="text-gray-700">{category.name}</span>
                        <Badge className="bg-purple-100 text-purple-600 hover:bg-purple-200">{category.count}</Badge>
                      </Link>
                    ))}
                  </div>
                </div>

                {/* Forum Stats */}
                <div className="bg-white rounded-lg shadow-sm p-4">
                  <h3 className="font-bold text-lg mb-4">Forum Stats</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Topics</span>
                      <span className="font-medium">156</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Posts</span>
                      <span className="font-medium">2,487</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Members</span>
                      <span className="font-medium">1,243</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Newest Member</span>
                      <Link href="/community/members/johndoe" className="text-purple-600 hover:underline">
                        JohnDoe
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Top Contributors */}
                <div className="bg-white rounded-lg shadow-sm p-4">
                  <h3 className="font-bold text-lg mb-4">Top Contributors</h3>
                  <div className="space-y-3">
                    {[
                      {
                        name: "Marie Dubois",
                        username: "@mariedubois",
                        posts: 124,
                        avatar: "/placeholder.svg?height=40&width=40",
                      },
                      {
                        name: "Jean Baptiste",
                        username: "@jeanbaptiste",
                        posts: 98,
                        avatar: "/placeholder.svg?height=40&width=40",
                      },
                      {
                        name: "Olivia Laporte",
                        username: "@olivialaporte",
                        posts: 87,
                        avatar: "/placeholder.svg?height=40&width=40",
                      },
                      {
                        name: "Michel Confait",
                        username: "@michelconfait",
                        posts: 76,
                        avatar: "/placeholder.svg?height=40&width=40",
                      },
                      {
                        name: "Sophia Payet",
                        username: "@sophiapayet",
                        posts: 65,
                        avatar: "/placeholder.svg?height=40&width=40",
                      },
                    ].map((contributor, index) => (
                      <Link
                        href={`/community/members/${contributor.username.substring(1)}`}
                        key={index}
                        className="flex items-center gap-3 py-2 hover:bg-purple-50 rounded-md px-2 transition-colors"
                      >
                        <div className="h-8 w-8 rounded-full bg-purple-100 overflow-hidden">
                          <Image
                            src={contributor.avatar || "/placeholder.svg"}
                            width={32}
                            height={32}
                            alt={contributor.name}
                            className="object-cover"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm truncate">{contributor.name}</p>
                          <p className="text-xs text-gray-500 truncate">{contributor.username}</p>
                        </div>
                        <Badge className="bg-purple-100 text-purple-600 hover:bg-purple-200">{contributor.posts}</Badge>
                      </Link>
                    ))}
                  </div>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:col-span-3">
                {/* Filter Controls */}
                <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-500">Sort by:</span>
                      <select className="text-sm border rounded-md px-2 py-1 bg-white border-gray-200">
                        <option>Latest Activity</option>
                        <option>Most Popular</option>
                        <option>Newest</option>
                        <option>Most Replies</option>
                      </select>
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-2 border-purple-200 text-purple-600 hover:bg-purple-50"
                      >
                        <Filter className="h-4 w-4" /> Filter
                      </Button>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" className="border-gray-200">
                        All Topics
                      </Button>
                      <Button variant="outline" size="sm" className="border-gray-200">
                        Unread
                      </Button>
                      <Button variant="outline" size="sm" className="border-gray-200">
                        Pinned
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Topics List */}
                <div className="space-y-4">
                  {/* Pinned Topics */}
                  <div className="bg-purple-50 rounded-lg p-3 border border-purple-100">
                    <h3 className="font-medium text-purple-800 mb-2">Pinned Topics</h3>
                    <div className="space-y-2">
                      {[
                        {
                          title: "Forum Rules and Guidelines",
                          author: { name: "Admin", avatar: "/placeholder.svg?height=40&width=40" },
                          replies: 24,
                          views: 1243,
                          lastActivity: "2 days ago",
                          isPinned: true,
                          isLocked: true,
                        },
                        {
                          title: "Creole Language Resources - Master List",
                          author: { name: "Marie Dubois", avatar: "/placeholder.svg?height=40&width=40" },
                          replies: 87,
                          views: 3456,
                          lastActivity: "1 day ago",
                          isPinned: true,
                        },
                      ].map((topic, index) => (
                        <TopicCard key={index} topic={topic} />
                      ))}
                    </div>
                  </div>

                  {/* Regular Topics */}
                  <div className="bg-white rounded-lg shadow-sm">
                    <div className="p-4 border-b border-gray-100">
                      <h3 className="font-medium text-gray-800">Topics</h3>
                    </div>
                    <div className="divide-y divide-gray-100">
                      {[
                        {
                          title: "Traditional Seychellois Recipes Collection",
                          author: { name: "Sophia Payet", avatar: "/placeholder.svg?height=40&width=40" },
                          replies: 32,
                          views: 876,
                          lastActivity: "3 hours ago",
                        },
                        {
                          title: "The Evolution of Moutya Dance Through Generations",
                          author: { name: "Jean Baptiste", avatar: "/placeholder.svg?height=40&width=40" },
                          replies: 45,
                          views: 1024,
                          lastActivity: "5 hours ago",
                        },
                        {
                          title: "Preserving Creole Language in Modern Seychelles",
                          author: { name: "Michel Confait", avatar: "/placeholder.svg?height=40&width=40" },
                          replies: 67,
                          views: 1532,
                          lastActivity: "12 hours ago",
                        },
                        {
                          title: "Traditional Fishing Techniques of Seychelles",
                          author: { name: "Robert Morin", avatar: "/placeholder.svg?height=40&width=40" },
                          replies: 28,
                          views: 743,
                          lastActivity: "1 day ago",
                        },
                        {
                          title: "The Significance of Coco de Mer in Seychellois Culture",
                          author: { name: "Olivia Laporte", avatar: "/placeholder.svg?height=40&width=40" },
                          replies: 53,
                          views: 1287,
                          lastActivity: "2 days ago",
                        },
                        {
                          title: "Celebrating Festival Kreol - Share Your Experiences",
                          author: { name: "Thomas Fanchette", avatar: "/placeholder.svg?height=40&width=40" },
                          replies: 41,
                          views: 968,
                          lastActivity: "3 days ago",
                        },
                        {
                          title: "Traditional Medicinal Plants of Seychelles",
                          author: { name: "Claire Delpech", avatar: "/placeholder.svg?height=40&width=40" },
                          replies: 36,
                          views: 821,
                          lastActivity: "4 days ago",
                        },
                        {
                          title: "The Art of Storytelling in Seychellois Families",
                          author: { name: "Paul Ernesta", avatar: "/placeholder.svg?height=40&width=40" },
                          replies: 29,
                          views: 674,
                          lastActivity: "5 days ago",
                        },
                      ].map((topic, index) => (
                        <TopicCard key={index} topic={topic} />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Pagination */}
                <div className="flex items-center justify-between mt-6">
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-1 border-purple-200 text-purple-600 hover:bg-purple-50"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="m15 18-6-6 6-6" />
                    </svg>{" "}
                    Previous
                  </Button>
                  <div className="flex items-center gap-1">
                    {[1, 2, 3, 4, 5].map((page) => (
                      <Button
                        key={page}
                        variant={page === 1 ? "default" : "outline"}
                        size="sm"
                        className={
                          page === 1 ? "bg-purple-600 hover:bg-purple-700 h-8 w-8 p-0" : "border-gray-200 h-8 w-8 p-0"
                        }
                      >
                        {page}
                      </Button>
                    ))}
                    <span className="mx-1">...</span>
                    <Button variant="outline" size="sm" className="border-gray-200 h-8 w-8 p-0">
                      12
                    </Button>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-1 border-purple-200 text-purple-600 hover:bg-purple-50"
                  >
                    Next{" "}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="m9 18 6-6-6-6" />
                    </svg>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t py-12">
        <div className="container px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-600">
                Seychelles' premier community platform connecting people with shared interests.
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Community</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Forums
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Groups
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Members
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Activity
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Resources</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Guidelines
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Safety Tips
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Moderation
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Cookie Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Community Standards
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Community. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-purple-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
              </Link>
              <Link href="#" className="text-gray-600 hover:text-purple-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                </svg>
              </Link>
              <Link href="#" className="text-gray-600 hover:text-purple-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="community" colorScheme="purple" />
    </div>
  )
}

// Topic Card Component
function TopicCard({ topic }) {
  return (
    <Link
      href={`/community/forums/local-culture-traditions/topic/${topic.title.toLowerCase().replace(/\s+/g, "-")}`}
      className="block"
    >
      <div className="p-4 hover:bg-gray-50 transition-colors">
        <div className="flex items-start gap-4">
          <div className="h-10 w-10 rounded-full bg-purple-100 overflow-hidden shrink-0">
            <Image
              src={topic.author.avatar || "/placeholder.svg"}
              width={40}
              height={40}
              alt={topic.author.name}
              className="object-cover"
            />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-gray-900 hover:text-purple-600 transition-colors line-clamp-1">
                {topic.title}
              </h3>
              {topic.isPinned && (
                <Badge className="bg-purple-100 text-purple-600">
                  <Pin className="h-3 w-3 mr-1" /> Pinned
                </Badge>
              )}
              {topic.isLocked && (
                <Badge className="bg-gray-100 text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-3 w-3 mr-1"
                  >
                    <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
                    <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                  </svg>{" "}
                  Locked
                </Badge>
              )}
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Started by <span className="text-purple-600">{topic.author.name}</span>
            </p>
            <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
              <div className="flex items-center gap-1">
                <MessageCircle className="h-3 w-3" />
                <span>{topic.replies} replies</span>
              </div>
              <div className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                <span>{topic.views} views</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{topic.lastActivity}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}

