"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Search,
  ShoppingBag,
  ShoppingCart,
  Heart,
  User,
  ChevronDown,
  Filter,
  Star,
  Store,
  ArrowRight,
  MapPin,
  Clock,
  TrendingUp,
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import FeatureNavigation from "@/components/feature-navigation"
import { Messenger } from "@/components/messenger"

export default function ShoppingPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <div className="hidden md:flex relative w-full max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search products, vendors, categories..."
                className="pl-10 pr-4 py-2 w-full border-emerald-200 focus:border-emerald-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-emerald-600">
              <Heart className="h-5 w-5" />
              <span className="sr-only">Wishlist</span>
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-emerald-600 relative">
              <ShoppingCart className="h-5 w-5" />
              <span className="sr-only">Cart</span>
              <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-emerald-600">
                3
              </Badge>
            </Button>
            <div className="flex items-center gap-2">
              <Button variant="ghost" className="text-gray-600 hover:text-emerald-600">
                <User className="h-5 w-5 mr-2" />
                <span className="hidden md:inline">Account</span>
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>
        <div className="container px-4 py-2 border-t border-gray-100">
          <nav className="flex items-center gap-6 overflow-x-auto pb-2 md:pb-0">
            <Link
              href="/shopping/category/clothing"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Clothing & Fashion
            </Link>
            <Link
              href="/shopping/category/crafts"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Local Crafts
            </Link>
            <Link
              href="/shopping/category/food"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Food & Groceries
            </Link>
            <Link
              href="/shopping/category/beauty"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Beauty & Wellness
            </Link>
            <Link
              href="/shopping/category/home"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Home & Garden
            </Link>
            <Link
              href="/shopping/category/electronics"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Electronics
            </Link>
            <Link
              href="/shopping/category/services"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Services
            </Link>
          </nav>
        </div>
      </header>

      <main>
        {/* Hero Banner */}
        <section className="relative bg-gradient-to-r from-emerald-600 to-teal-500 text-white py-12 md:py-20">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="space-y-4">
                <h1 className="text-3xl md:text-5xl font-bold tracking-tighter">
                  Discover Seychelles' Finest Local Products
                </h1>
                <p className="text-lg md:text-xl text-emerald-50">
                  Shop from trusted local vendors and artisans. Support the Seychellois economy while finding unique
                  treasures.
                </p>
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button className="bg-white text-emerald-600 hover:bg-emerald-50">Shop Now</Button>
                  <Button variant="outline" className="bg-transparent border-white text-white hover:bg-white/10">
                    Become a Vendor
                  </Button>
                </div>
              </div>
              <div className="relative h-[300px] lg:h-[400px] rounded-lg overflow-hidden">
                <Image
                  src="/placeholder.svg?height=400&width=600"
                  fill
                  alt="Seychelles marketplace"
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Featured Vendors */}
        <section className="py-12 bg-white">
          <div className="container px-4 md:px-6">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold">Featured Vendors</h2>
              <Link
                href="/shopping/vendors"
                className="text-emerald-600 hover:text-emerald-700 text-sm font-medium flex items-center"
              >
                View All Vendors <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((vendor) => (
                <VendorCard key={vendor} />
              ))}
            </div>
          </div>
        </section>

        {/* Product Categories */}
        <section className="py-12 bg-gray-50">
          <div className="container px-4 md:px-6">
            <h2 className="text-2xl font-bold mb-8">Shop by Category</h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <CategoryCard title="Clothing" icon={<ShoppingBag className="h-8 w-8 text-emerald-600" />} />
              <CategoryCard title="Local Crafts" icon={<Store className="h-8 w-8 text-emerald-600" />} />
              <CategoryCard title="Food & Groceries" icon={<ShoppingCart className="h-8 w-8 text-emerald-600" />} />
              <CategoryCard title="Beauty" icon={<Heart className="h-8 w-8 text-emerald-600" />} />
              <CategoryCard title="Home & Garden" icon={<MapPin className="h-8 w-8 text-emerald-600" />} />
              <CategoryCard title="Services" icon={<User className="h-8 w-8 text-emerald-600" />} />
            </div>
          </div>
        </section>

        {/* Product Listings */}
        <section className="py-12 bg-white">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
              <h2 className="text-2xl font-bold">Popular Products</h2>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Sort by:</span>
                  <select className="text-sm border rounded-md px-2 py-1 bg-white">
                    <option>Popularity</option>
                    <option>Newest</option>
                    <option>Price: Low to High</option>
                    <option>Price: High to Low</option>
                    <option>Rating</option>
                  </select>
                </div>
                <Button variant="outline" size="sm" className="gap-2">
                  <Filter className="h-4 w-4" /> Filter
                </Button>
              </div>
            </div>

            <Tabs defaultValue="all" className="mb-8">
              <TabsList className="grid grid-cols-4 md:w-[400px]">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="trending">Trending</TabsTrigger>
                <TabsTrigger value="new">New Arrivals</TabsTrigger>
                <TabsTrigger value="sale">On Sale</TabsTrigger>
              </TabsList>
              <TabsContent value="all" className="mt-6">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-6">
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((product) => (
                    <ProductCard key={product} />
                  ))}
                </div>
              </TabsContent>
              <TabsContent value="trending" className="mt-6">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-6">
                  {[1, 2, 3, 4, 5].map((product) => (
                    <ProductCard key={product} trending={true} />
                  ))}
                </div>
              </TabsContent>
              <TabsContent value="new" className="mt-6">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-6">
                  {[1, 2, 3, 4, 5].map((product) => (
                    <ProductCard key={product} isNew={true} />
                  ))}
                </div>
              </TabsContent>
              <TabsContent value="sale" className="mt-6">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-6">
                  {[1, 2, 3, 4, 5].map((product) => (
                    <ProductCard key={product} onSale={true} />
                  ))}
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex justify-center mt-8">
              <Button variant="outline" className="text-emerald-600 border-emerald-600 hover:bg-emerald-50">
                Load More Products
              </Button>
            </div>
          </div>
        </section>

        {/* Vendor Spotlight */}
        <section className="py-12 bg-emerald-50">
          <div className="container px-4 md:px-6">
            <h2 className="text-2xl font-bold mb-8">Vendor Spotlight</h2>
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="relative h-[300px] md:h-auto">
                  <Image
                    src="/placeholder.svg?height=400&width=600"
                    fill
                    alt="Vendor spotlight"
                    className="object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 flex flex-col justify-center">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="h-12 w-12 rounded-full bg-emerald-100 flex items-center justify-center">
                      <Store className="h-6 w-6 text-emerald-600" />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg">Island Crafts Co.</h3>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                        <span className="text-sm text-gray-500 ml-1">(48 reviews)</span>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-6">
                    Island Crafts Co. specializes in handmade Seychellois crafts using sustainable materials. Each piece
                    tells a story of our island heritage and supports local artisans.
                  </p>
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-emerald-600" />
                      <span className="text-sm">Victoria, Mahé</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-emerald-600" />
                      <span className="text-sm">Joined 2 years ago</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ShoppingBag className="h-4 w-4 text-emerald-600" />
                      <span className="text-sm">124 Products</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-emerald-600" />
                      <span className="text-sm">500+ Sales</span>
                    </div>
                  </div>
                  <Button className="bg-emerald-600 hover:bg-emerald-700 text-white w-full md:w-auto">
                    Visit Store
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Recently Added */}
        <section className="py-12 bg-white">
          <div className="container px-4 md:px-6">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold">Recently Added</h2>
              <Link
                href="/shopping/new"
                className="text-emerald-600 hover:text-emerald-700 text-sm font-medium flex items-center"
              >
                View All New Products <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {[1, 2, 3, 4, 5, 6].map((product) => (
                <ProductCard key={product} isNew={true} compact={true} />
              ))}
            </div>
          </div>
        </section>

        {/* Become a Vendor */}
        <section className="py-12 bg-gradient-to-r from-emerald-600 to-teal-500 text-white">
          <div className="container px-4 md:px-6">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <h2 className="text-3xl font-bold">Become a Vendor on Kominote</h2>
                <p className="text-emerald-50">
                  Join our growing marketplace of Seychellois vendors. Reach more customers, grow your business, and be
                  part of our thriving digital community.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-3 w-3 text-emerald-600"
                      >
                        <polyline points="20 6 9 17 4 12" />
                      </svg>
                    </div>
                    <span>Easy onboarding process</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-3 w-3 text-emerald-600"
                      >
                        <polyline points="20 6 9 17 4 12" />
                      </svg>
                    </div>
                    <span>Access to thousands of local customers</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-3 w-3 text-emerald-600"
                      >
                        <polyline points="20 6 9 17 4 12" />
                      </svg>
                    </div>
                    <span>Secure payment processing</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-3 w-3 text-emerald-600"
                      >
                        <polyline points="20 6 9 17 4 12" />
                      </svg>
                    </div>
                    <span>Marketing and promotional opportunities</span>
                  </li>
                </ul>
                <Button className="bg-white text-emerald-600 hover:bg-emerald-50 mt-4">Apply Now</Button>
              </div>
              <div className="relative h-[300px] rounded-lg overflow-hidden">
                <Image
                  src="/placeholder.svg?height=400&width=600"
                  fill
                  alt="Vendor selling products"
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t py-12">
        <div className="container px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-600">
                Seychelles' premier multivendor marketplace connecting local vendors with customers.
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Shopping</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    All Categories
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Deals & Promotions
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    New Arrivals
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Best Sellers
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Vendors</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Become a Vendor
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Vendor Directory
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Vendor Resources
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Success Stories
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Help & Support</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Customer Service
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Shipping & Delivery
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Returns & Refunds
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    FAQ
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Shopping. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Terms of Service
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Privacy Policy
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="shopping" colorScheme="emerald" />

      {/* Messenger - with light variant for visibility on light backgrounds */}
      <Messenger variant="light" />
    </div>
  )
}

// Vendor Card Component
function VendorCard() {
  return (
    <Link href="/shopping/vendor/island-crafts" className="group">
      <div className="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-200 hover:shadow-lg">
        <div className="relative h-40">
          <Image src="/placeholder.svg?height=160&width=320" fill alt="Vendor banner" className="object-cover" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          <div className="absolute bottom-4 left-4 right-4 flex items-center gap-3">
            <div className="h-12 w-12 rounded-full bg-white p-0.5">
              <div className="h-full w-full rounded-full bg-emerald-100 flex items-center justify-center">
                <Store className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
            <div>
              <h3 className="font-bold text-white">Island Crafts Co.</h3>
              <div className="flex items-center">
                <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                <span className="text-xs text-white ml-1">(48)</span>
              </div>
            </div>
          </div>
        </div>
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <span className="text-xs text-gray-500 flex items-center gap-1">
              <MapPin className="h-3 w-3" /> Victoria, Mahé
            </span>
            <span className="text-xs text-gray-500">124 Products</span>
          </div>
          <p className="text-sm text-gray-600 line-clamp-2">
            Handmade Seychellois crafts using sustainable materials. Each piece tells a story of our island heritage.
          </p>
          <div className="mt-4 pt-4 border-t border-gray-100 flex justify-between items-center">
            <span className="text-xs text-emerald-600 font-medium">View Store</span>
            <div className="flex -space-x-2">
              {[1, 2, 3].map((item) => (
                <div
                  key={item}
                  className="h-6 w-6 rounded-full bg-gray-200 border border-white overflow-hidden relative"
                >
                  <Image
                    src="/placeholder.svg?height=24&width=24"
                    fill
                    alt="Product thumbnail"
                    className="object-cover"
                  />
                </div>
              ))}
              <div className="h-6 w-6 rounded-full bg-emerald-100 border border-white flex items-center justify-center text-xs text-emerald-600 font-medium">
                +12
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}

// Category Card Component
function CategoryCard({ title, icon }) {
  return (
    <Link href={`/shopping/category/${title.toLowerCase().replace(/\s+/g, "-")}`} className="group">
      <div className="bg-white rounded-lg shadow-sm p-6 flex flex-col items-center text-center transition-all duration-200 hover:shadow-md hover:bg-emerald-50">
        <div className="h-16 w-16 rounded-full bg-emerald-100 flex items-center justify-center mb-4 group-hover:bg-emerald-200 transition-colors">
          {icon}
        </div>
        <h3 className="font-medium text-gray-800">{title}</h3>
      </div>
    </Link>
  )
}

// Product Card Component
function ProductCard({ trending = false, isNew = false, onSale = false, compact = false }) {
  return (
    <Link href="/shopping/product/handmade-coconut-bowl" className="group">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md">
        <div className="relative">
          <div className="aspect-square overflow-hidden">
            <Image
              src="/placeholder.svg?height=300&width=300"
              width={300}
              height={300}
              alt="Product image"
              className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
            />
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full bg-white/80 text-gray-600 hover:text-emerald-600 hover:bg-white"
          >
            <Heart className="h-4 w-4" />
          </Button>
          {trending && <Badge className="absolute top-2 left-2 bg-blue-600">Trending</Badge>}
          {isNew && <Badge className="absolute top-2 left-2 bg-emerald-600">New</Badge>}
          {onSale && <Badge className="absolute top-2 left-2 bg-red-600">Sale</Badge>}
        </div>
        <div className="p-3">
          {!compact && (
            <div className="flex items-center gap-1 mb-1">
              <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
              <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
              <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
              <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
              <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
              <span className="text-xs text-gray-500 ml-1">(24)</span>
            </div>
          )}
          <h3 className="font-medium text-gray-800 line-clamp-1 group-hover:text-emerald-600 transition-colors">
            Handmade Coconut Bowl
          </h3>
          {!compact && <p className="text-xs text-gray-500 mt-1 line-clamp-1">Island Crafts Co.</p>}
          <div className="flex items-center justify-between mt-2">
            {onSale ? (
              <div className="flex items-center gap-1">
                <span className="text-sm font-bold text-emerald-600">$24.99</span>
                <span className="text-xs text-gray-500 line-through">$34.99</span>
              </div>
            ) : (
              <span className="text-sm font-bold text-emerald-600">$24.99</span>
            )}
            {!compact && (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 rounded-full bg-emerald-100 text-emerald-600 hover:bg-emerald-200"
              >
                <ShoppingCart className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </Link>
  )
}

