@echo off
REM Kominote Apps Creation Script
REM This script creates all the Django apps for Phase 2

echo ========================================
echo Creating Kominote Django Apps
echo ========================================

echo Activating virtual environment...
call kominote_env\Scripts\activate

echo Creating Django apps in apps directory...

echo Creating accounts app...
python manage.py startapp accounts apps/accounts

echo Creating landing app...
python manage.py startapp landing apps/landing

echo Creating streaming app...
python manage.py startapp streaming apps/streaming

echo Creating shopping app...
python manage.py startapp shopping apps/shopping

echo Creating community app...
python manage.py startapp community apps/community

echo Creating events app...
python manage.py startapp events apps/events

echo Creating jobs app...
python manage.py startapp jobs apps/jobs

echo Creating api app...
python manage.py startapp api apps/api

echo ========================================
echo All apps created successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Add apps to INSTALLED_APPS in settings.py
echo 2. Create models for each app
echo 3. Create API views and serializers
echo 4. Configure URLs for each app
echo ========================================

pause
