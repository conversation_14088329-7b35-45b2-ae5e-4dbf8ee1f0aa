"use client"

import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Filter, ChevronRight, Play } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { UserNav } from "@/components/user-nav"
import FeatureNavigation from "@/components/feature-navigation"

export default function MoviesPage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-gradient-to-b from-black/80 to-transparent backdrop-blur-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <nav className="hidden md:flex gap-6">
              <Link href="/streaming" className="text-sm font-medium text-gray-300 hover:text-white">
                Home
              </Link>
              <Link href="/streaming/series" className="text-sm font-medium text-gray-300 hover:text-white">
                Series
              </Link>
              <Link href="/streaming/movies" className="text-sm font-medium text-white hover:text-blue-400">
                Movies
              </Link>
              <Link href="/streaming/new" className="text-sm font-medium text-gray-300 hover:text-white">
                New & Popular
              </Link>
              <Link href="/streaming/mylist" className="text-sm font-medium text-gray-300 hover:text-white">
                My List
              </Link>
            </nav>
          </div>
          <div className="flex items-center gap-4">
            <div className="relative hidden md:flex items-center">
              <Search className="absolute left-2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search movies..."
                className="w-[220px] pl-8 bg-black/20 border-gray-700 focus:border-blue-500 text-sm text-white placeholder:text-gray-400"
              />
            </div>
            <Button variant="ghost" size="icon" className="text-gray-300 hover:text-white md:hidden">
              <Search className="h-5 w-5" />
              <span className="sr-only">Search</span>
            </Button>
            <UserNav />
          </div>
        </div>
      </header>

      <main>
        {/* Hero Banner */}
        <section className="relative h-[50vh] w-full">
          <Image
            src="/placeholder.svg?height=1080&width=1920"
            fill
            alt="Seychelles Movies"
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/60 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900/90 to-transparent" />
          <div className="absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-center px-4 md:px-12 space-y-4">
            <div className="max-w-lg">
              <Badge className="mb-4 bg-blue-600">Movies</Badge>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Local Seychellois Films</h1>
              <p className="text-lg text-gray-200 mb-6">
                Discover the best movies produced in Seychelles, showcasing our culture, landscapes, and stories.
              </p>
            </div>
          </div>
        </section>

        {/* Filter and Search Section */}
        <section className="py-6 px-4 md:px-12 bg-gray-900">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="relative w-full md:w-auto">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search all movies..."
                className="w-full md:w-[300px] pl-10 bg-gray-800 border-gray-700 focus:border-blue-500 text-sm text-white placeholder:text-gray-400"
              />
            </div>
            <div className="flex items-center gap-3 w-full md:w-auto">
              <Button variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:text-white">
                <Filter className="mr-2 h-4 w-4" /> Filter
              </Button>
              <select className="bg-gray-800 border border-gray-700 rounded-md text-sm p-2 text-gray-300">
                <option>Sort By: Newest</option>
                <option>Sort By: Oldest</option>
                <option>Sort By: A-Z</option>
                <option>Sort By: Rating</option>
              </select>
            </div>
          </div>
        </section>

        {/* Movies by Genre */}
        <section className="py-8 px-4 md:px-12">
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="bg-gray-800/60 mb-6">
              <TabsTrigger value="all" className="data-[state=active]:bg-blue-600">
                All Genres
              </TabsTrigger>
              <TabsTrigger value="documentary" className="data-[state=active]:bg-blue-600">
                Documentary
              </TabsTrigger>
              <TabsTrigger value="drama" className="data-[state=active]:bg-blue-600">
                Drama
              </TabsTrigger>
              <TabsTrigger value="comedy" className="data-[state=active]:bg-blue-600">
                Comedy
              </TabsTrigger>
              <TabsTrigger value="culture" className="data-[state=active]:bg-blue-600">
                Cultural
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-0">
              {/* Featured Movies */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Featured Movies</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "island-paradise",
                      title: "Island Paradise",
                      year: "2022",
                      genre: "Documentary",
                      rating: "8.7",
                    },
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      year: "2023",
                      genre: "Documentary",
                      rating: "9.1",
                    },
                    {
                      id: "the-last-fisherman",
                      title: "The Last Fisherman",
                      year: "2021",
                      genre: "Drama",
                      rating: "8.5",
                    },
                    {
                      id: "ocean-guardians",
                      title: "Ocean Guardians",
                      year: "2022",
                      genre: "Documentary",
                      rating: "8.9",
                    },
                    { id: "island-life", title: "Island Life", year: "2023", genre: "Comedy", rating: "7.8" },
                  ].map((movie) => (
                    <MovieCard
                      key={movie.id}
                      id={movie.id}
                      title={movie.title}
                      year={movie.year}
                      genre={movie.genre}
                      rating={movie.rating}
                    />
                  ))}
                </div>
              </div>

              {/* New Releases */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">New Releases</h2>
                  <Link href="/streaming/new">
                    <Button variant="ghost" className="text-gray-300 hover:text-white text-sm">
                      See All <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      year: "2023",
                      genre: "Documentary",
                      rating: "9.1",
                      isNew: true,
                    },
                    {
                      id: "island-life",
                      title: "Island Life",
                      year: "2023",
                      genre: "Comedy",
                      rating: "7.8",
                      isNew: true,
                    },
                    {
                      id: "festival-of-lights",
                      title: "Festival of Lights",
                      year: "2023",
                      genre: "Cultural",
                      rating: "8.2",
                      isNew: true,
                    },
                    {
                      id: "hidden-beaches",
                      title: "Hidden Beaches",
                      year: "2023",
                      genre: "Documentary",
                      rating: "8.6",
                      isNew: true,
                    },
                    {
                      id: "island-dreams",
                      title: "Island Dreams",
                      year: "2023",
                      genre: "Drama",
                      rating: "7.9",
                      isNew: true,
                    },
                  ].map((movie) => (
                    <MovieCard
                      key={movie.id}
                      id={movie.id}
                      title={movie.title}
                      year={movie.year}
                      genre={movie.genre}
                      rating={movie.rating}
                      isNew={movie.isNew}
                    />
                  ))}
                </div>
              </div>

              {/* All Movies */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">All Movies</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "island-paradise",
                      title: "Island Paradise",
                      year: "2022",
                      genre: "Documentary",
                      rating: "8.7",
                    },
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      year: "2023",
                      genre: "Documentary",
                      rating: "9.1",
                    },
                    {
                      id: "the-last-fisherman",
                      title: "The Last Fisherman",
                      year: "2021",
                      genre: "Drama",
                      rating: "8.5",
                    },
                    {
                      id: "ocean-guardians",
                      title: "Ocean Guardians",
                      year: "2022",
                      genre: "Documentary",
                      rating: "8.9",
                    },
                    { id: "island-life", title: "Island Life", year: "2023", genre: "Comedy", rating: "7.8" },
                    {
                      id: "festival-of-lights",
                      title: "Festival of Lights",
                      year: "2023",
                      genre: "Cultural",
                      rating: "8.2",
                    },
                    {
                      id: "hidden-beaches",
                      title: "Hidden Beaches",
                      year: "2023",
                      genre: "Documentary",
                      rating: "8.6",
                    },
                    { id: "island-dreams", title: "Island Dreams", year: "2023", genre: "Drama", rating: "7.9" },
                    { id: "tourist-troubles", title: "Tourist Troubles", year: "2022", genre: "Comedy", rating: "7.5" },
                    { id: "aldabra-atoll", title: "Aldabra Atoll", year: "2021", genre: "Documentary", rating: "9.3" },
                    { id: "creole-heritage", title: "Creole Heritage", year: "2022", genre: "Cultural", rating: "8.4" },
                    {
                      id: "traditional-fishing",
                      title: "Traditional Fishing",
                      year: "2021",
                      genre: "Documentary",
                      rating: "8.1",
                    },
                    { id: "the-return", title: "The Return", year: "2022", genre: "Drama", rating: "8.3" },
                    { id: "local-legends", title: "Local Legends", year: "2021", genre: "Cultural", rating: "7.7" },
                  ].map((movie) => (
                    <MovieCard
                      key={movie.id}
                      id={movie.id}
                      title={movie.title}
                      year={movie.year}
                      genre={movie.genre}
                      rating={movie.rating}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="documentary" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Documentary Films</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "island-paradise",
                      title: "Island Paradise",
                      year: "2022",
                      genre: "Documentary",
                      rating: "8.7",
                    },
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      year: "2023",
                      genre: "Documentary",
                      rating: "9.1",
                    },
                    {
                      id: "ocean-guardians",
                      title: "Ocean Guardians",
                      year: "2022",
                      genre: "Documentary",
                      rating: "8.9",
                    },
                    {
                      id: "hidden-beaches",
                      title: "Hidden Beaches",
                      year: "2023",
                      genre: "Documentary",
                      rating: "8.6",
                    },
                    { id: "aldabra-atoll", title: "Aldabra Atoll", year: "2021", genre: "Documentary", rating: "9.3" },
                    {
                      id: "traditional-fishing",
                      title: "Traditional Fishing",
                      year: "2021",
                      genre: "Documentary",
                      rating: "8.1",
                    },
                  ].map((movie) => (
                    <MovieCard
                      key={movie.id}
                      id={movie.id}
                      title={movie.title}
                      year={movie.year}
                      genre={movie.genre}
                      rating={movie.rating}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="drama" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Drama Films</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "the-last-fisherman",
                      title: "The Last Fisherman",
                      year: "2021",
                      genre: "Drama",
                      rating: "8.5",
                    },
                    { id: "island-dreams", title: "Island Dreams", year: "2023", genre: "Drama", rating: "7.9" },
                    { id: "the-return", title: "The Return", year: "2022", genre: "Drama", rating: "8.3" },
                  ].map((movie) => (
                    <MovieCard
                      key={movie.id}
                      id={movie.id}
                      title={movie.title}
                      year={movie.year}
                      genre={movie.genre}
                      rating={movie.rating}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="comedy" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Comedy Films</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    { id: "island-life", title: "Island Life", year: "2023", genre: "Comedy", rating: "7.8" },
                    { id: "tourist-troubles", title: "Tourist Troubles", year: "2022", genre: "Comedy", rating: "7.5" },
                  ].map((movie) => (
                    <MovieCard
                      key={movie.id}
                      id={movie.id}
                      title={movie.title}
                      year={movie.year}
                      genre={movie.genre}
                      rating={movie.rating}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="culture" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Cultural Films</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "festival-of-lights",
                      title: "Festival of Lights",
                      year: "2023",
                      genre: "Cultural",
                      rating: "8.2",
                    },
                    { id: "creole-heritage", title: "Creole Heritage", year: "2022", genre: "Cultural", rating: "8.4" },
                    { id: "local-legends", title: "Local Legends", year: "2021", genre: "Cultural", rating: "7.7" },
                  ].map((movie) => (
                    <MovieCard
                      key={movie.id}
                      id={movie.id}
                      title={movie.title}
                      year={movie.year}
                      genre={movie.genre}
                      rating={movie.rating}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-800 py-8">
        <div className="container px-4 md:px-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-400">Seychelles' premier streaming platform for local content</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Browse</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/streaming/series" className="text-sm text-gray-400 hover:text-white">
                    Series
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/movies" className="text-sm text-gray-400 hover:text-white">
                    Movies
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/new" className="text-sm text-gray-400 hover:text-white">
                    New & Popular
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/mylist" className="text-sm text-gray-400 hover:text-white">
                    My List
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Help</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Account
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Devices
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Terms of Use
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Cookie Preferences
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Corporate Information
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800 text-center">
            <p className="text-xs text-gray-400">
              &copy; {new Date().getFullYear()} Kominote Streaming. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="streaming" colorScheme="blue" />
    </div>
  )
}

// Movie Card Component
function MovieCard({ id, title, year, genre, rating, isNew = false }) {
  return (
    <div className="group relative overflow-hidden rounded-md cursor-pointer transition-transform duration-200 hover:scale-105 hover:z-10">
      <Image
        src="/placeholder.svg?height=270&width=180"
        width={180}
        height={270}
        alt={title}
        className="object-cover w-full h-full aspect-[2/3] transition-transform duration-300 group-hover:scale-110"
      />
      {isNew && <Badge className="absolute top-2 left-2 bg-blue-600">New</Badge>}
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute bottom-0 left-0 right-0 p-3 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
        <div className="flex items-center gap-2">
          <Button
            size="icon"
            className="h-8 w-8 rounded-full bg-blue-600 hover:bg-blue-700"
            onClick={(e) => {
              e.stopPropagation()
              window.location.href = `/streaming/watch/${id}`
            }}
          >
            <Play className="h-4 w-4" />
          </Button>
        </div>
        <h3 className="mt-2 text-sm font-medium line-clamp-1">{title}</h3>
        <div className="flex items-center gap-2 mt-1">
          <span className="text-xs text-gray-400">{year}</span>
          <span className="text-xs text-gray-400">•</span>
          <span className="text-xs text-gray-400">{genre}</span>
        </div>
        <div className="flex items-center mt-1">
          <span className="text-xs text-yellow-400">★ {rating}</span>
        </div>
      </div>
      {/* Clickable overlay for the entire card */}
      <Link href={`/streaming/details/${id}`} className="absolute inset-0">
        <span className="sr-only">View details for {title}</span>
      </Link>
    </div>
  )
}

