"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Search,
  Calendar,
  MapPin,
  Users,
  Filter,
  ChevronDown,
  Bell,
  ArrowRight,
  Plus,
  CalendarDays,
  CalendarClock,
  Ticket,
  Music,
  Briefcase,
  Palette,
  Bike,
  Heart,
  Share2,
  Star,
  BookmarkPlus,
} from "lucide-react"
import FeatureNavigation from "@/components/feature-navigation"
import { Messenger } from "@/components/messenger"

function Utensils(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2" />
      <path d="M7 2v20" />
      <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7" />
    </svg>
  )
}

function Camera(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z" />
      <circle cx="12" cy="13" r="3" />
    </svg>
  )
}

function Map(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <polygon points="3 6 9 3 15 6 21 3 21 18 15 21 9 18 3 21" />
      <line x1="9" x2="9" y1="3" y2="18" />
      <line x1="15" x2="15" y1="6" y2="21" />
    </svg>
  )
}

export default function EventsPage() {
  return (
    <div>
      {/* Events page content */}
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
          <div className="container flex h-16 items-center justify-between px-4">
            <div className="flex items-center gap-8">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <div className="hidden md:flex relative w-full max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="search"
                  placeholder="Search events, venues, or organizers..."
                  className="pl-10 pr-4 py-2 w-full border-amber-200 focus:border-amber-500"
                />
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="icon" className="text-gray-600 hover:text-amber-600 relative">
                <Bell className="h-5 w-5" />
                <span className="sr-only">Notifications</span>
                <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-amber-600">
                  2
                </Badge>
              </Button>
              <div className="flex items-center gap-2">
                <Button variant="ghost" className="text-gray-600 hover:text-amber-600">
                  <div className="h-8 w-8 rounded-full bg-amber-100 mr-2 overflow-hidden">
                    <Image
                      src="/placeholder.svg?height=32&width=32"
                      width={32}
                      height={32}
                      alt="Profile"
                      className="object-cover"
                    />
                  </div>
                  <span className="hidden md:inline">Sarah Johnson</span>
                  <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </div>
          <div className="container px-4 py-2 border-t border-gray-100">
            <nav className="flex items-center gap-6 overflow-x-auto pb-2 md:pb-0">
              <Link
                href="/events"
                className="text-sm font-medium whitespace-nowrap text-amber-600 border-b-2 border-amber-600 pb-1"
              >
                All Events
              </Link>
              <Link
                href="/events/today"
                className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-amber-600"
              >
                Today
              </Link>
              <Link
                href="/events/weekend"
                className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-amber-600"
              >
                This Weekend
              </Link>
              <Link
                href="/events/free"
                className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-amber-600"
              >
                Free Events
              </Link>
              <Link
                href="/events/my-events"
                className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-amber-600"
              >
                My Events
              </Link>
              <Link
                href="/events/create"
                className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-amber-600"
              >
                Create Event
              </Link>
            </nav>
          </div>
        </header>

        <main>
          {/* Hero Banner */}
          <section className="relative bg-gradient-to-r from-amber-600 to-orange-500 text-white py-12 md:py-20">
            <div className="container px-4 md:px-6">
              <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
                <div className="space-y-4">
                  <h1 className="text-3xl md:text-5xl font-bold tracking-tighter">
                    Discover Exciting Events in Seychelles
                  </h1>
                  <p className="text-lg md:text-xl text-amber-50">
                    From cultural festivals to business networking, find what's happening around you and never miss a
                    moment.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button className="bg-white text-amber-600 hover:bg-amber-50">Browse Events</Button>
                    <Button variant="outline" className="bg-transparent border-white text-white hover:bg-white/10">
                      <Calendar className="mr-2 h-4 w-4" /> Create Event
                    </Button>
                  </div>
                </div>
                <div className="relative h-[300px] lg:h-[400px] rounded-lg overflow-hidden">
                  <Image
                    src="/placeholder.svg?height=400&width=600"
                    fill
                    alt="Seychelles events"
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </section>

          {/* Featured Events */}
          <section className="py-12 bg-white">
            <div className="container px-4 md:px-6">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-bold">Featured Events</h2>
                <div className="flex items-center gap-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2 border-amber-200 text-amber-600 hover:bg-amber-50"
                  >
                    <Filter className="h-4 w-4" /> Filter
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FeaturedEventCard
                  title="Seychelles International Cultural Festival"
                  date="June 18-20, 2023"
                  location="Victoria, Mahé"
                  image="/placeholder.svg?height=300&width=500"
                  category="Cultural"
                  attendees={1240}
                  isFeatured={true}
                />
                <FeaturedEventCard
                  title="Creole Food & Music Festival"
                  date="July 5-7, 2023"
                  location="Beau Vallon Beach"
                  image="/placeholder.svg?height=300&width=500"
                  category="Food & Music"
                  attendees={850}
                  isFeatured={true}
                />
                <FeaturedEventCard
                  title="Seychelles Ocean Conservation Summit"
                  date="July 15, 2023"
                  location="Eden Island Conference Center"
                  image="/placeholder.svg?height=300&width=500"
                  category="Conference"
                  attendees={320}
                  isFeatured={true}
                />
              </div>
            </div>
          </section>

          {/* Event Categories */}
          <section className="py-12 bg-gray-50">
            <div className="container px-4 md:px-6">
              <h2 className="text-2xl font-bold mb-8">Browse by Category</h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-8 gap-4">
                <CategoryCard title="Music" icon={<Music className="h-8 w-8 text-amber-600" />} count={42} />
                <CategoryCard title="Food & Drink" icon={<Utensils className="h-8 w-8 text-amber-600" />} count={38} />
                <CategoryCard title="Business" icon={<Briefcase className="h-8 w-8 text-amber-600" />} count={24} />
                <CategoryCard title="Arts" icon={<Palette className="h-8 w-8 text-amber-600" />} count={31} />
                <CategoryCard title="Sports" icon={<Bike className="h-8 w-8 text-amber-600" />} count={27} />
                <CategoryCard title="Cultural" icon={<Users className="h-8 w-8 text-amber-600" />} count={19} />
                <CategoryCard
                  title="Workshops"
                  icon={<CalendarClock className="h-8 w-8 text-amber-600" />}
                  count={15}
                />
                <CategoryCard title="Festivals" icon={<Ticket className="h-8 w-8 text-amber-600" />} count={12} />
              </div>
            </div>
          </section>

          {/* Upcoming Events */}
          <section className="py-12 bg-white">
            <div className="container px-4 md:px-6">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-bold">Upcoming Events</h2>
                <Link
                  href="/events/upcoming"
                  className="text-amber-600 hover:text-amber-700 text-sm font-medium flex items-center"
                >
                  View All <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>

              <Tabs defaultValue="all" className="mb-8">
                <TabsList className="grid grid-cols-4 md:w-[400px]">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="this-week">This Week</TabsTrigger>
                  <TabsTrigger value="this-month">This Month</TabsTrigger>
                  <TabsTrigger value="free">Free</TabsTrigger>
                </TabsList>
                <TabsContent value="all" className="mt-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {[1, 2, 3, 4, 5, 6].map((event) => (
                      <EventCard key={event} />
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="this-week" className="mt-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {[1, 2, 3].map((event) => (
                      <EventCard key={event} isThisWeek={true} />
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="this-month" className="mt-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {[1, 2, 3, 4].map((event) => (
                      <EventCard key={event} isThisMonth={true} />
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="free" className="mt-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {[1, 2].map((event) => (
                      <EventCard key={event} isFree={true} />
                    ))}
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-center mt-8">
                <Button variant="outline" className="text-amber-600 border-amber-600 hover:bg-amber-50">
                  Load More Events
                </Button>
              </div>
            </div>
          </section>

          {/* Popular Venues */}
          <section className="py-12 bg-amber-50">
            <div className="container px-4 md:px-6">
              <h2 className="text-2xl font-bold mb-8">Popular Venues</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[
                  {
                    name: "Eden Island Marina",
                    location: "Eden Island, Mahé",
                    events: 24,
                    image: "/placeholder.svg?height=200&width=300",
                    rating: 4.8,
                  },
                  {
                    name: "Beau Vallon Beach",
                    location: "Beau Vallon, Mahé",
                    events: 18,
                    image: "/placeholder.svg?height=200&width=300",
                    rating: 4.9,
                  },
                  {
                    name: "National Theatre",
                    location: "Victoria, Mahé",
                    events: 15,
                    image: "/placeholder.svg?height=200&width=300",
                    rating: 4.6,
                  },
                  {
                    name: "La Digue Community Center",
                    location: "La Digue Island",
                    events: 12,
                    image: "/placeholder.svg?height=200&width=300",
                    rating: 4.7,
                  },
                ].map((venue, index) => (
                  <VenueCard
                    key={index}
                    name={venue.name}
                    location={venue.location}
                    events={venue.events}
                    image={venue.image}
                    rating={venue.rating}
                  />
                ))}
              </div>
            </div>
          </section>

          {/* Event Calendar */}
          <section className="py-12 bg-white">
            <div className="container px-4 md:px-6">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-bold">Event Calendar</h2>
                <Button className="bg-amber-600 hover:bg-amber-700 gap-2">
                  <CalendarDays className="h-4 w-4" /> View Full Calendar
                </Button>
              </div>
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
                  {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map((day, index) => (
                    <div key={index} className="text-center font-medium text-gray-500">
                      {day}
                    </div>
                  ))}
                  {Array.from({ length: 31 }, (_, i) => i + 1).map((date) => (
                    <div
                      key={date}
                      className={`h-24 border rounded-md p-1 ${
                        date === 15
                          ? "bg-amber-100 border-amber-300"
                          : date === 18 || date === 22 || date === 25
                            ? "bg-amber-50 border-amber-200"
                            : "border-gray-200"
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <span className="text-sm font-medium">{date}</span>
                        {(date === 15 || date === 18 || date === 22 || date === 25) && (
                          <Badge className="bg-amber-600">{date === 15 ? "3" : "1"}</Badge>
                        )}
                      </div>
                      {date === 15 && (
                        <div className="mt-1">
                          <div className="text-xs bg-amber-600 text-white rounded px-1 py-0.5 mb-1 truncate">
                            Cultural Festival
                          </div>
                          <div className="text-xs bg-blue-600 text-white rounded px-1 py-0.5 mb-1 truncate">
                            Beach Concert
                          </div>
                        </div>
                      )}
                      {date === 18 && (
                        <div className="mt-1">
                          <div className="text-xs bg-green-600 text-white rounded px-1 py-0.5 truncate">
                            Eco Workshop
                          </div>
                        </div>
                      )}
                      {date === 22 && (
                        <div className="mt-1">
                          <div className="text-xs bg-purple-600 text-white rounded px-1 py-0.5 truncate">
                            Art Exhibition
                          </div>
                        </div>
                      )}
                      {date === 25 && (
                        <div className="mt-1">
                          <div className="text-xs bg-red-600 text-white rounded px-1 py-0.5 truncate">
                            Food Festival
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Create Event CTA */}
          <section className="py-12 bg-gradient-to-r from-amber-600 to-orange-500 text-white">
            <div className="container px-4 md:px-6">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-4">
                  <h2 className="text-3xl font-bold">Host Your Own Event</h2>
                  <p className="text-amber-50">
                    Whether it's a small gathering or a large festival, share your event with the Seychelles community.
                    It's easy to create, manage, and promote your events on Kominote.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-amber-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span>Easy event creation and management</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-amber-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span>Reach thousands of local attendees</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-amber-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span>Ticket sales and registration tools</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-white flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-amber-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span>Promotion across Kominote platform</span>
                    </li>
                  </ul>
                  <Button className="bg-white text-amber-600 hover:bg-amber-50 mt-4">
                    <Plus className="mr-2 h-4 w-4" /> Create Event
                  </Button>
                </div>
                <div className="relative h-[300px] rounded-lg overflow-hidden">
                  <Image
                    src="/placeholder.svg?height=400&width=600"
                    fill
                    alt="Event hosting"
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </section>
        </main>

        {/* Footer */}
        <footer className="bg-white border-t py-12">
          <div className="container px-4 md:px-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="space-y-4">
                <Link href="/" className="flex items-center space-x-2">
                  <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                    Kominote
                  </span>
                </Link>
                <p className="text-sm text-gray-600">
                  Seychelles' premier events platform connecting organizers with attendees.
                </p>
              </div>
              <div className="space-y-4">
                <h3 className="font-medium">Events</h3>
                <ul className="space-y-2">
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      Browse Events
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      Create Event
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      Calendar
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      Venues
                    </Link>
                  </li>
                </ul>
              </div>
              <div className="space-y-4">
                <h3 className="font-medium">Organizers</h3>
                <ul className="space-y-2">
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      Event Management
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      Ticketing
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      Promotion Tools
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      Success Stories
                    </Link>
                  </li>
                </ul>
              </div>
              <div className="space-y-4">
                <h3 className="font-medium">Help & Support</h3>
                <ul className="space-y-2">
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      FAQs
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      Contact Us
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      Terms & Conditions
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                      Privacy Policy
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
            <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
              <p className="text-sm text-gray-600">
                &copy; {new Date().getFullYear()} Kominote Events. All rights reserved.
              </p>
              <div className="flex items-center gap-4 mt-4 md:mt-0">
                <Link href="#" className="text-gray-600 hover:text-amber-600">
                  Terms of Service
                </Link>
                <Link href="#" className="text-gray-600 hover:text-amber-600">
                  Privacy Policy
                </Link>
                <Link href="#" className="text-gray-600 hover:text-amber-600">
                  Cookie Policy
                </Link>
              </div>
            </div>
          </div>
        </footer>

        {/* Side Navigation */}
        <FeatureNavigation currentFeature="events" colorScheme="amber" />
      </div>

      {/* Messenger - with light variant for visibility on light backgrounds */}
      <Messenger variant="light" />
    </div>
  )
}

// Featured Event Card Component
function FeaturedEventCard({ title, date, location, image, category, attendees, isFeatured }) {
  return (
    <Card className="overflow-hidden border-amber-200 hover:border-amber-300 transition-colors">
      <div className="relative h-48">
        <Image src={image || "/placeholder.svg"} fill alt={title} className="object-cover" />
        <div className="absolute top-2 left-2">
          <Badge className="bg-amber-600">{category}</Badge>
        </div>
        {isFeatured && (
          <div className="absolute top-2 right-2">
            <Badge className="bg-yellow-500 flex items-center gap-1">
              <Star className="h-3 w-3 fill-white" /> Featured
            </Badge>
          </div>
        )}
      </div>
      <CardHeader className="pb-2">
        <CardTitle className="text-xl">{title}</CardTitle>
        <CardDescription>
          <div className="flex items-center gap-1 text-amber-600">
            <Calendar className="h-4 w-4" />
            <span>{date}</span>
          </div>
          <div className="flex items-center gap-1 text-gray-500 mt-1">
            <MapPin className="h-4 w-4" />
            <span>{location}</span>
          </div>
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="flex items-center gap-1 text-gray-500">
          <Users className="h-4 w-4" />
          <span>{attendees} attending</span>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" size="sm" className="border-amber-200 text-amber-600 hover:bg-amber-50">
          <Heart className="h-4 w-4 mr-1" /> Interested
        </Button>
        <Button className="bg-amber-600 hover:bg-amber-700">View Details</Button>
      </CardFooter>
    </Card>
  )
}

// Category Card Component
function CategoryCard({ title, icon, count }) {
  return (
    <Link href={`/events/category/${title.toLowerCase().replace(/\s+/g, "-")}`} className="group">
      <div className="bg-white rounded-lg shadow-sm p-6 flex flex-col items-center text-center transition-all duration-200 hover:shadow-md hover:bg-amber-50">
        <div className="h-16 w-16 rounded-full bg-amber-100 flex items-center justify-center mb-4 group-hover:bg-amber-200 transition-colors">
          {icon}
        </div>
        <h3 className="font-medium text-gray-800">{title}</h3>
        <span className="text-xs text-gray-500 mt-1">{count} events</span>
      </div>
    </Link>
  )
}

// Event Card Component
function EventCard({ isThisWeek = false, isThisMonth = false, isFree = false }) {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 hover:border-amber-300 transition-colors">
      <div className="relative h-40">
        <Image src="/placeholder.svg?height=160&width=320" fill alt="Event" className="object-cover" />
        {isFree && (
          <div className="absolute top-2 left-2">
            <Badge className="bg-green-600">Free</Badge>
          </div>
        )}
        {isThisWeek && (
          <div className="absolute top-2 left-2">
            <Badge className="bg-blue-600">This Week</Badge>
          </div>
        )}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
          <h3 className="font-bold text-white">Beach Cleanup & Conservation Talk</h3>
        </div>
      </div>
      <div className="p-4">
        <div className="flex items-center gap-1 text-amber-600 mb-2">
          <Calendar className="h-4 w-4" />
          <span className="text-sm">Sat, July 8, 2023 • 9:00 AM</span>
        </div>
        <div className="flex items-center gap-1 text-gray-500 mb-3">
          <MapPin className="h-4 w-4" />
          <span className="text-sm">Beau Vallon Beach, Mahé</span>
        </div>
        <p className="text-sm text-gray-600 mb-4 line-clamp-2">
          Join us for a community beach cleanup followed by an informative talk about marine conservation efforts in
          Seychelles.
        </p>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Users className="h-4 w-4 text-gray-500" />
            <span className="text-xs text-gray-500">87 attending</span>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full text-gray-500 hover:text-amber-600">
              <BookmarkPlus className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full text-gray-500 hover:text-amber-600">
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      <div className="px-4 py-3 border-t border-gray-100 flex justify-between items-center">
        <span className="text-sm font-medium text-amber-600">Free</span>
        <Button size="sm" className="bg-amber-600 hover:bg-amber-700">
          Register
        </Button>
      </div>
    </div>
  )
}

// Venue Card Component
function VenueCard({ name, location, events, image, rating }) {
  return (
    <Link href={`/events/venue/${name.toLowerCase().replace(/\s+/g, "-")}`} className="group">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md">
        <div className="relative h-40">
          <Image src={image || "/placeholder.svg"} fill alt={name} className="object-cover" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          <div className="absolute bottom-3 left-3 right-3">
            <h3 className="font-bold text-white">{name}</h3>
            <div className="flex items-center gap-1">
              <MapPin className="h-3 w-3 text-amber-300" />
              <span className="text-xs text-white">{location}</span>
            </div>
          </div>
        </div>
        <div className="p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
              <span className="text-sm font-medium">{rating}</span>
              <span className="text-xs text-gray-500">(32 reviews)</span>
            </div>
            <span className="text-xs text-amber-600 font-medium">{events} upcoming events</span>
          </div>
          <Button variant="outline" size="sm" className="w-full mt-2 border-amber-200 text-amber-600 hover:bg-amber-50">
            View Venue
          </Button>
        </div>
      </div>
    </Link>
  )
}

